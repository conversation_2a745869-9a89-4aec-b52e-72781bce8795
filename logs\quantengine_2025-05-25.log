2025-05-25 16:00:53 - quantengine - INFO - 数据源加载完成
2025-05-25 16:00:53 - quantengine - INFO - 正在加载策略: dual_ma
2025-05-25 16:00:53 - quantengine - INFO - 策略加载成功，类名: DualMaStrategy，模块: dual_ma，参数: {}
2025-05-25 16:00:53 - quantengine - INFO - 正在创建输出目录: output
2025-05-25 16:00:53 - quantengine - INFO - 开始执行回测...
2025-05-25 16:00:53 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2020-12-31
2025-05-25 16:00:53 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-25 16:00:53 - quantengine - INFO - 开始执行策略计算...
2025-05-25 16:00:55 - quantengine.strategy - INFO - Generated 144 entry signals and 80 exit signals
2025-05-25 16:00:55 - quantengine - INFO - 策略计算完成，生成144个入场信号和80个出场信号
2025-05-25 16:00:55 - quantengine - INFO - 开始执行回测计算...
2025-05-25 16:01:02 - quantengine - INFO - 生成了 5 条交易记录
2025-05-25 16:01:02 - quantengine - INFO - 回测完成，最终资产:57219.16
2025-05-25 16:01:02 - quantengine - INFO - 回测执行完成
2025-05-25 16:01:02 - quantengine - INFO - 开始生成报告...
2025-05-25 16:01:02 - quantengine.report - INFO - 开始生成回测报告...
2025-05-25 16:01:08 - quantengine.report - INFO - 将报告保存到 output
2025-05-25 16:01:08 - quantengine.report - INFO - 报告生成完成
2025-05-25 16:01:08 - quantengine - INFO - 报告生成完成，已保存至 output
2025-05-25 16:14:42 - quantengine - INFO - 数据源加载完成
2025-05-25 16:14:42 - quantengine - INFO - 正在加载策略: dual_ma
2025-05-25 16:14:42 - quantengine - INFO - 策略加载成功，类名: DualMaStrategy，模块: dual_ma，参数: {}
2025-05-25 16:14:42 - quantengine - INFO - 正在创建输出目录: output
2025-05-25 16:14:42 - quantengine - INFO - 开始执行回测...
2025-05-25 16:14:42 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2020-12-31
2025-05-25 16:14:42 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-25 16:14:42 - quantengine - INFO - 开始执行策略计算...
2025-05-25 16:14:44 - quantengine.strategy - INFO - Generated 144 entry signals and 80 exit signals
2025-05-25 16:14:44 - quantengine - INFO - 策略计算完成，生成144个入场信号和80个出场信号
2025-05-25 16:14:44 - quantengine - INFO - 开始执行回测计算...
2025-05-25 16:14:52 - quantengine - INFO - 生成了 5 条交易记录
2025-05-25 16:14:52 - quantengine - INFO - 回测完成，最终资产:57219.16
2025-05-25 16:14:52 - quantengine - INFO - 回测执行完成
2025-05-25 16:14:52 - quantengine - INFO - 开始生成报告...
2025-05-25 16:14:52 - quantengine.report - INFO - 开始生成回测报告...
2025-05-25 16:14:58 - quantengine.report - INFO - 将报告保存到 output
2025-05-25 16:14:58 - quantengine.report - INFO - 报告生成完成
2025-05-25 16:14:58 - quantengine - INFO - 报告生成完成，已保存至 output
2025-05-25 16:19:45 - quantengine - INFO - 数据源加载完成
2025-05-25 16:19:45 - quantengine - INFO - 正在加载策略: dual_ma
2025-05-25 16:19:45 - quantengine - INFO - 策略加载成功，类名: DualMaStrategy，模块: dual_ma，参数: {}
2025-05-25 16:19:45 - quantengine - INFO - 正在创建输出目录: output
2025-05-25 16:19:45 - quantengine - INFO - 开始执行回测...
2025-05-25 16:19:45 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2020-12-31
2025-05-25 16:19:45 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-25 16:19:45 - quantengine - INFO - 开始执行策略计算...
2025-05-25 16:19:47 - quantengine.strategy - INFO - Generated 144 entry signals and 80 exit signals
2025-05-25 16:19:47 - quantengine - INFO - 策略计算完成，生成144个入场信号和80个出场信号
2025-05-25 16:19:47 - quantengine - INFO - 开始执行回测计算...
2025-05-25 16:19:55 - quantengine - INFO - 生成了 5 条交易记录
2025-05-25 16:19:55 - quantengine - INFO - 回测完成，最终资产:57219.16
2025-05-25 16:19:55 - quantengine - INFO - 回测执行完成
2025-05-25 16:19:55 - quantengine - INFO - 开始生成报告...
2025-05-25 16:19:55 - quantengine.report - INFO - 开始生成回测报告...
2025-05-25 16:20:01 - quantengine.report - INFO - 将报告保存到 output
2025-05-25 16:20:44 - quantengine - INFO - 数据源加载完成
2025-05-25 16:20:44 - quantengine - INFO - 正在加载策略: dual_ma
2025-05-25 16:20:44 - quantengine - INFO - 策略加载成功，类名: DualMaStrategy，模块: dual_ma，参数: {}
2025-05-25 16:20:44 - quantengine - INFO - 正在创建输出目录: output
2025-05-25 16:20:44 - quantengine - INFO - 开始执行回测...
2025-05-25 16:20:44 - quantengine - INFO - 开始获取sh603768数据，时间范围:2020-01-01至2020-12-31
2025-05-25 16:20:44 - quantengine - INFO - 数据获取完成，共243条记录
2025-05-25 16:20:44 - quantengine - INFO - 开始执行策略计算...
2025-05-25 16:20:46 - quantengine.strategy - INFO - Generated 144 entry signals and 80 exit signals
2025-05-25 16:20:46 - quantengine - INFO - 策略计算完成，生成144个入场信号和80个出场信号
2025-05-25 16:20:46 - quantengine - INFO - 开始执行回测计算...
2025-05-25 16:20:54 - quantengine - INFO - 生成了 5 条交易记录
2025-05-25 16:20:54 - quantengine - INFO - 回测完成，最终资产:57219.16
2025-05-25 16:20:54 - quantengine - INFO - 回测执行完成
2025-05-25 16:20:54 - quantengine - INFO - 开始生成报告...
2025-05-25 16:20:54 - quantengine.report - INFO - 开始生成回测报告...
2025-05-25 16:20:59 - quantengine.report - INFO - 将报告保存到 output
2025-05-25 16:20:59 - quantengine.report - INFO - 报告生成完成
2025-05-25 16:20:59 - quantengine - INFO - 报告生成完成，已保存至 output
