import pandas as pd
import numpy as np
import vectorbt as vbt
import logging
from typing import List, Dict, Union, Optional, Any
from quantengine.data.data_source import DataSource
from quantengine.strategies.base_strategy import BaseStrategy

class BacktestEngine:
    """回测引擎，整合数据源和策略"""

    def __init__(self, data_source: DataSource, strategy: BaseStrategy, initial_capital: float = 100000.0,
                 commission: float = 0.0003, slippage: float = 0.001, position_size: float = 1.0):
        """
        初始化回测引擎

        参数:
            data_source (DataSource): 数据源实例
            strategy (BaseStrategy): 策略实例
            initial_capital (float): 初始资金
            commission (float): 手续费率
            slippage (float): 滑点率
            position_size (float): 仓位比例，1.0表示满仓
        """
        self.data_source = data_source
        self.strategy = strategy
        self.initial_capital = initial_capital
        self.commission = commission
        self.slippage = slippage
        self.position_size = position_size

    def run(self, symbol: str, start_date: str, end_date: str, timeframe: str = '1d') -> vbt.Portfolio:
        """
        运行回测

        参数:
            symbol (str): 证券代码
            start_date (str): 开始日期
            end_date (str): 结束日期
            timeframe (str): 时间周期

        返回:
            vbt.Portfolio: 回测结果
        """
        # 获取数据
        logger = logging.getLogger('quantengine')
        logger.info(f'开始获取{symbol}数据，时间范围:{start_date}至{end_date}')
        data = self.data_source.get_data(symbol, start_date, end_date, timeframe)
        logger.info(f'数据获取完成，共{len(data)}条记录')

        # 运行策略获取信号
        logger.info('开始执行策略计算...')
        entries, exits = self.strategy.run(data)
        logger.info(f'策略计算完成，生成{entries.sum()}个入场信号和{exits.sum()}个出场信号')

        # 使用VectorBT进行回测
        logger.info('开始执行回测计算...')

        # 确保信号是布尔类型
        entries = entries.astype(bool)
        exits = exits.astype(bool)

        # 检查信号是否有效
        if entries.sum() == 0:
            logger.warning('没有入场信号，回测结果可能不正确')
        if exits.sum() == 0:
            logger.warning('没有出场信号，回测结果可能不正确')

        # 打印前几个信号的日期，用于调试
        entry_dates = data.index[entries].tolist()[:5]
        exit_dates = data.index[exits].tolist()[:5]
        logger.debug(f'前5个入场信号日期: {entry_dates}')
        logger.debug(f'前5个出场信号日期: {exit_dates}')

        # 添加日志输出，检查传递给VectorBT的数据和信号
        logger.debug(f'Entries head:\n{entries.head()}')
        logger.debug(f'Entries tail:\n{entries.tail()}')
        logger.debug(f'Entries sum: {entries.sum()}')
        logger.debug(f'Entries isnull().sum(): {entries.isnull().sum()}')
        logger.debug(f'Exits head:\n{exits.head()}')
        logger.debug(f'Exits tail:\n{exits.tail()}')
        logger.debug(f'Exits sum: {exits.sum()}')
        logger.debug(f'Exits isnull().sum(): {exits.isnull().sum()}')
        logger.debug(f"Close data head:\n{data['close'].head()}")
        logger.debug(f"Close data tail:\n{data['close'].tail()}")
        logger.debug(f"Close data isnull().sum(): {data['close'].isnull().sum()}")

        # 创建投资组合
        portfolio = vbt.Portfolio.from_signals(
            data['close'],
            entries,
            exits,
            init_cash=self.initial_capital,
            fees=self.commission,
            slippage=self.slippage,
            size=self.position_size,
            size_type='percent',
            freq=timeframe
        )

        # 检查交易记录
        trade_count = portfolio.trades.count()
        logger.info(f'生成了 {trade_count} 条交易记录')

        # 如果没有交易记录但有信号，尝试使用不同的参数重新计算
        if trade_count == 0 and (entries.sum() > 0 or exits.sum() > 0):
            logger.warning('没有生成交易记录，尝试使用不同的参数重新计算...')
            # 尝试使用不同的参数
            portfolio = vbt.Portfolio.from_signals(
                data['close'],
                entries,
                exits,
                init_cash=self.initial_capital,
                fees=self.commission,
                slippage=self.slippage,
                size=self.position_size,
                size_type='percent',
                freq=timeframe,
                accumulate=True,  # 允许累积仓位
                call_seq='auto'   # 自动确定调用顺序
            )
            logger.info(f'重新计算后生成了 {portfolio.trades.count()} 条交易记录')

        logger.info(f'回测完成，最终资产:{portfolio.total_profit():.2f}')

        return portfolio

    def run_multiple(self, symbols: List[str], start_date: str, end_date: str, timeframe: str = '1d') -> vbt.Portfolio:
        """
        运行多资产回测

        参数:
            symbols (list): 证券代码列表
            start_date (str): 开始日期
            end_date (str): 结束日期
            timeframe (str): 时间周期

        返回:
            vbt.Portfolio: 回测结果
        """
        # 获取多个资产的数据
        all_data = {}
        for symbol in symbols:
            data = self.data_source.get_data(symbol, start_date, end_date, timeframe)
            all_data[symbol] = data['close']

        # 合并数据
        close = pd.DataFrame(all_data)

        # 为每个资产运行策略
        entries = {}
        exits = {}
        for symbol in symbols:
            symbol_data = self.data_source.get_data(symbol, start_date, end_date, timeframe)
            entry, exit = self.strategy.run(symbol_data)
            entries[symbol] = entry
            exits[symbol] = exit

        # 转换为DataFrame
        entries_df = pd.DataFrame(entries)
        exits_df = pd.DataFrame(exits)

        # 使用VectorBT进行多资产回测
        portfolio = vbt.Portfolio.from_signals(
            close,
            entries_df,
            exits_df,
            init_cash=self.initial_capital,
            fees=self.commission,
            slippage=self.slippage,
            size=self.position_size / len(symbols),  # 平均分配资金
            freq=timeframe
        )

        return portfolio
