import akshare as ak
import pandas as pd
import numpy as np
import logging
from datetime import datetime
from .data_source import DataSource
from ..utils.logger import get_default_logger

class AkshareSource(DataSource):
    """基于Akshare的数据源实现"""
    
    def __init__(self):
        """初始化Akshare数据源"""
        self.logger = get_default_logger()

    def get_data(self, symbol, start_date, end_date, timeframe='1d'):
        """
        从Akshare获取数据

        参数:
            symbol (str): 证券代码，例如 'sh000001', 'sz000001', 'sh510050'
            start_date (str): 开始日期，格式：YYYY-MM-DD
            end_date (str): 结束日期，格式：YYYY-MM-DD
            timeframe (str): 时间周期，支持 '1d'（日线）, '1h'（小时线）, '1m'（分钟线）

        返回:
            pd.DataFrame: 包含OHLCV数据的DataFrame
        """
        # 转换日期格式
        start_date_fmt = start_date.replace('-', '')
        end_date_fmt = end_date.replace('-', '')

        df = None

        # 根据时间周期获取不同的数据
        if timeframe == '1d':
            # 处理不同类型的证券
            if symbol.startswith('sh') or symbol.startswith('sz'):  # A股
                # 移除前缀获取纯数字代码
                code = symbol.replace('sh', '').replace('sz', '')

                try:
                    df = ak.stock_zh_a_hist(
                        symbol=code,
                        period="daily",
                        start_date=start_date_fmt,
                        end_date=end_date_fmt,
                        adjust="qfq"  # 前复权
                    )
                except Exception as e:
                    self.logger.error(f"获取股票数据失败 - 代码: {symbol}, 错误: {str(e)}")
                    # 返回空的DataFrame，保持一致的结构
                    return pd.DataFrame(columns=['open', 'high', 'low', 'close', 'volume'])

            elif symbol.startswith('sh5') or symbol.startswith('sz1'):  # ETF
                try:
                    # 移除前缀获取纯数字代码
                    code = symbol.replace('sh', '').replace('sz', '')
                    df = ak.fund_etf_hist_sina(
                        symbol=code,
                        period="daily"
                    )
                    # 筛选日期范围
                    df = df[(df['date'] >= start_date) & (df['date'] <= end_date)]
                except Exception as e:
                    self.logger.error(f"获取ETF数据失败 - 代码: {symbol}, 错误: {str(e)}")
                    return pd.DataFrame(columns=['open', 'high', 'low', 'close', 'volume'])

            elif symbol.startswith('sz12') or symbol.startswith('sh11'):  # 可转债
                try:
                    df = ak.bond_zh_hs_cov_daily(symbol=symbol)
                    # 筛选日期范围
                    df = df[(df['date'] >= start_date) & (df['date'] <= end_date)]
                except Exception as e:
                    self.logger.error(f"获取可转债数据失败 - 代码: {symbol}, 错误: {str(e)}")
                    return pd.DataFrame(columns=['open', 'high', 'low', 'close', 'volume'])

            elif symbol.startswith('IF') or symbol.startswith('IC') or symbol.startswith('IH'):  # 股指期货
                try:
                    df = ak.futures_zh_daily_sina(symbol=symbol)
                    # 筛选日期范围
                    df = df[(df['date'] >= start_date) & (df['date'] <= end_date)]
                except Exception as e:
                    self.logger.error(f"获取期货数据失败 - 代码: {symbol}, 错误: {str(e)}")
                    return pd.DataFrame(columns=['open', 'high', 'low', 'close', 'volume'])
            else:
                raise ValueError(f"不支持的证券类型: {symbol}")

        elif timeframe == '1h':
            # 小时级数据
            if symbol.startswith('sh') or symbol.startswith('sz'):  # A股
                try:
                    code = symbol.replace('sh', '').replace('sz', '')
                    df = ak.stock_zh_a_hist_min_em(
                        symbol=code,
                        period='60',  # 60分钟
                        start_date=start_date,
                        end_date=end_date
                    )
                except Exception as e:
                    error_msg = f"获取小时级股票数据失败 - 代码: {symbol}, 时间范围: {start_date}至{end_date}, 错误详情: {str(e)}"
                    self.logger.error(error_msg)
                    self.logger.debug(f"请求参数: symbol={code}, start_date={start_date}, end_date={end_date}, period=60")
                    return pd.DataFrame(columns=['open', 'high', 'low', 'close', 'volume'])
            else:
                raise ValueError(f"不支持的小时级数据类型: {symbol}")

        elif timeframe == '1m':
            # 分钟级数据
            if symbol.startswith('sh') or symbol.startswith('sz'):  # A股
                try:
                    code = symbol.replace('sh', '').replace('sz', '')
                    df = ak.stock_zh_a_hist_min_em(
                        symbol=code,
                        period='1',  # 1分钟
                        start_date=start_date,
                        end_date=end_date
                    )
                except Exception as e:
                    error_msg = f"获取分钟级股票数据失败 - 代码: {symbol}, 时间范围: {start_date}至{end_date}, 错误详情: {str(e)}"
                    self.logger.error(error_msg)
                    self.logger.debug(f"请求参数: symbol={code}, start_date={start_date}, end_date={end_date}, period=1")
                    return pd.DataFrame(columns=['open', 'high', 'low', 'close', 'volume'])
            else:
                raise ValueError(f"不支持的分钟级数据类型: {symbol}")
        else:
            raise ValueError(f"不支持的时间周期: {timeframe}")

        # 如果获取数据失败，返回空DataFrame
        if df is None or df.empty:
            error_msg = f"未获取到有效数据 - 代码: {symbol}, 时间范围: {start_date}至{end_date}, 时间周期: {timeframe}"
            self.logger.warning(error_msg)
            self.logger.debug(f"返回空DataFrame以避免下游处理错误")
            return pd.DataFrame(columns=['open', 'high', 'low', 'close', 'volume'])

        # 标准化列名
        df = self._standardize_columns(df)
        return df

    def _standardize_columns(self, df):
        """
        标准化数据列名

        参数:
            df (pd.DataFrame): 原始数据

        返回:
            pd.DataFrame: 标准化后的数据
        """
        # 常见的列名映射
        column_mapping = {
            # A股数据
            '日期': 'date',
            '开盘': 'open',
            '收盘': 'close',
            '最高': 'high',
            '最低': 'low',
            '成交量': 'volume',
            '成交额': 'turnover',
            '涨跌幅': 'change_pct',
            '涨跌额': 'change',
            '换手率': 'turnover_rate',

            # ETF数据
            'date': 'date',
            'open': 'open',
            'high': 'high',
            'low': 'low',
            'close': 'close',
            'volume': 'volume',

            # 分钟数据
            '时间': 'date',
            '开盘价': 'open',
            '最高价': 'high',
            '最低价': 'low',
            '收盘价': 'close',
            '成交量': 'volume',
            '成交额': 'turnover',

            # 其他可能的列名
            'trade_date': 'date',
            'open_price': 'open',
            'high_price': 'high',
            'low_price': 'low',
            'close_price': 'close',
            'vol': 'volume',
            'amount': 'turnover'
        }

        # 重命名列
        renamed_df = df.rename(columns={k: v for k, v in column_mapping.items() if k in df.columns})

        # 确保必要的列存在
        required_columns = ['date', 'open', 'high', 'low', 'close', 'volume']
        for col in required_columns:
            if col not in renamed_df.columns:
                # 如果缺少必要的列，尝试从其他列推断或设置默认值
                if col == 'date' and '日期' in df.columns:
                    renamed_df['date'] = df['日期']
                elif col == 'volume' and 'vol' in df.columns:
                    renamed_df['volume'] = df['vol']
                elif col not in ['date', 'volume']:
                    # 对于价格列，如果缺失，使用其他价格列的值
                    if 'close' in renamed_df.columns and col in ['open', 'high', 'low']:
                        renamed_df[col] = renamed_df['close']
                    else:
                        # 最后的选择：设置为NaN
                        renamed_df[col] = np.nan

        # 确保日期列是日期时间类型
        if 'date' in renamed_df.columns:
            renamed_df['date'] = pd.to_datetime(renamed_df['date'])
            renamed_df.set_index('date', inplace=True)

        return renamed_df