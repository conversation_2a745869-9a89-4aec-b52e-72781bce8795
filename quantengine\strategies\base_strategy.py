from abc import ABC, abstractmethod
from typing import Dict, Tuple, Any, Optional
import pandas as pd
import vectorbt as vbt
import numpy as np

class BaseStrategy(ABC):
    """策略基类，定义策略接口"""
    
    def __init__(self, params: Optional[Dict[str, Any]] = None):
        """
        初始化策略
        
        参数:
            params (dict): 策略参数
        """
        self.params = params or {}
        
    @staticmethod
    def get_params_schema() -> Dict[str, Any]:
        """
        获取策略参数的验证模式
        
        返回:
            dict: 参数验证模式，用于前端验证
        """
        return {}
    
    @abstractmethod
    def run(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        运行策略
        
        参数:
            data (pd.DataFrame): 输入数据
            
        返回:
            tuple: (entries, exits) 买入和卖出信号
        """
        pass