"""技术指标计算模块"""
import pandas as pd
import numpy as np
import vectorbt as vbt
from typing import Tuple, Union, Optional


def moving_average(data: pd.DataFrame, window: int, column: str = 'close') -> pd.Series:
    """
    计算移动平均线
    
    参数:
        data (pd.DataFrame): 包含价格数据的DataFrame
        window (int): 移动平均窗口大小
        column (str): 用于计算的列名，默认为'close'
        
    返回:
        pd.Series: 移动平均线
    """
    return data[column].rolling(window=window).mean()


def exponential_moving_average(data: pd.DataFrame, window: int, column: str = 'close') -> pd.Series:
    """
    计算指数移动平均线
    
    参数:
        data (pd.DataFrame): 包含价格数据的DataFrame
        window (int): 移动平均窗口大小
        column (str): 用于计算的列名，默认为'close'
        
    返回:
        pd.Series: 指数移动平均线
    """
    return data[column].ewm(span=window, adjust=False).mean()


def relative_strength_index(data: pd.DataFrame, window: int = 14, column: str = 'close') -> pd.Series:
    """
    计算相对强弱指标(RSI)
    
    参数:
        data (pd.DataFrame): 包含价格数据的DataFrame
        window (int): 计算窗口大小，默认为14
        column (str): 用于计算的列名，默认为'close'
        
    返回:
        pd.Series: RSI值
    """
    delta = data[column].diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    
    avg_gain = gain.rolling(window=window).mean()
    avg_loss = loss.rolling(window=window).mean()
    
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    
    return rsi


def bollinger_bands(data: pd.DataFrame, window: int = 20, num_std: float = 2, column: str = 'close') -> Tuple[pd.Series, pd.Series, pd.Series]:
    """
    计算布林带
    
    参数:
        data (pd.DataFrame): 包含价格数据的DataFrame
        window (int): 移动平均窗口大小，默认为20
        num_std (float): 标准差的倍数，默认为2
        column (str): 用于计算的列名，默认为'close'
        
    返回:
        tuple: (中轨, 上轨, 下轨)
    """
    middle_band = data[column].rolling(window=window).mean()
    std = data[column].rolling(window=window).std()
    
    upper_band = middle_band + (std * num_std)
    lower_band = middle_band - (std * num_std)
    
    return middle_band, upper_band, lower_band


def kdj(data: pd.DataFrame, n: int = 9, m1: int = 3, m2: int = 3) -> Tuple[pd.Series, pd.Series, pd.Series]:
    """
    计算KDJ指标
    
    参数:
        data (pd.DataFrame): 包含价格数据的DataFrame，必须包含'high', 'low', 'close'列
        n (int): 计算周期，默认为9
        m1 (int): K值平滑因子，默认为3
        m2 (int): D值平滑因子，默认为3
        
    返回:
        tuple: (K值, D值, J值)
    """
    # 计算N日内的最高价和最低价
    high_n = data['high'].rolling(n).max()
    low_n = data['low'].rolling(n).min()
    
    # 计算未成熟随机值RSV
    rsv = 100 * (data['close'] - low_n) / (high_n - low_n)
    rsv = rsv.fillna(50)  # 填充NaN值
    
    # 计算K值、D值和J值
    k = pd.Series(0.0, index=data.index)
    d = pd.Series(0.0, index=data.index)
    
    for i in range(len(data)):
        if i == 0:
            k[i] = 50.0
            d[i] = 50.0
        else:
            k[i] = (m1 - 1) * k[i-1] / m1 + rsv[i] / m1
            d[i] = (m2 - 1) * d[i-1] / m2 + k[i] / m2
    
    j = 3 * k - 2 * d
    
    return k, d, j


def average_true_range(data: pd.DataFrame, window: int = 14) -> pd.Series:
    """
    计算平均真实范围(ATR)指标
    
    参数:
        data (pd.DataFrame): 包含价格数据的DataFrame，必须包含'high', 'low', 'close'列
        window (int): 计算周期，默认为14
        
    返回:
        pd.Series: ATR值
    """
    # 计算真实范围(TR)
    high = data['high']
    low = data['low']
    close = data['close']
    
    # 第一种情况：当日最高价 - 当日最低价
    tr1 = high - low
    
    # 第二种情况：|当日最高价 - 昨日收盘价|
    tr2 = abs(high - close.shift(1))
    
    # 第三种情况：|当日最低价 - 昨日收盘价|
    tr3 = abs(low - close.shift(1))
    
    # 取三种情况的最大值作为真实范围
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    
    # 计算ATR
    atr = tr.rolling(window=window).mean()
    
    return atr


def macd(data: pd.DataFrame, fast_period: int = 12, slow_period: int = 26, signal_period: int = 9, column: str = 'close') -> Tuple[pd.Series, pd.Series, pd.Series]:
    """
    计算MACD指标
    
    参数:
        data (pd.DataFrame): 包含价格数据的DataFrame
        fast_period (int): 快线周期，默认为12
        slow_period (int): 慢线周期，默认为26
        signal_period (int): 信号线周期，默认为9
        column (str): 用于计算的列名，默认为'close'
        
    返回:
        tuple: (MACD线, 信号线, 柱状图)
    """
    ema_fast = data[column].ewm(span=fast_period, adjust=False).mean()
    ema_slow = data[column].ewm(span=slow_period, adjust=False).mean()
    
    macd_line = ema_fast - ema_slow
    signal_line = macd_line.ewm(span=signal_period, adjust=False).mean()
    histogram = macd_line - signal_line
    
    return macd_line, signal_line, histogram


def stochastic_oscillator(data: pd.DataFrame, k_period: int = 14, d_period: int = 3) -> Tuple[pd.Series, pd.Series]:
    """
    计算随机震荡指标
    
    参数:
        data (pd.DataFrame): 包含OHLC数据的DataFrame
        k_period (int): %K周期，默认为14
        d_period (int): %D周期，默认为3
        
    返回:
        tuple: (%K, %D)
    """
    low_min = data['low'].rolling(window=k_period).min()
    high_max = data['high'].rolling(window=k_period).max()
    
    k = 100 * ((data['close'] - low_min) / (high_max - low_min))
    d = k.rolling(window=d_period).mean()
    
    return k, d


def average_true_range(data: pd.DataFrame, window: int = 14) -> pd.Series:
    """
    计算平均真实范围(ATR)
    
    参数:
        data (pd.DataFrame): 包含OHLC数据的DataFrame
        window (int): 计算窗口大小，默认为14
        
    返回:
        pd.Series: ATR值
    """
    high_low = data['high'] - data['low']
    high_close = (data['high'] - data['close'].shift()).abs()
    low_close = (data['low'] - data['close'].shift()).abs()
    
    ranges = pd.concat([high_low, high_close, low_close], axis=1)
    true_range = ranges.max(axis=1)
    
    return true_range.rolling(window=window).mean()


def on_balance_volume(data: pd.DataFrame) -> pd.Series:
    """
    计算能量潮指标(OBV)
    
    参数:
        data (pd.DataFrame): 包含价格和成交量数据的DataFrame
        
    返回:
        pd.Series: OBV值
    """
    obv = pd.Series(index=data.index, dtype='float64')
    obv.iloc[0] = 0
    
    for i in range(1, len(data)):
        if data['close'].iloc[i] > data['close'].iloc[i-1]:
            obv.iloc[i] = obv.iloc[i-1] + data['volume'].iloc[i]
        elif data['close'].iloc[i] < data['close'].iloc[i-1]:
            obv.iloc[i] = obv.iloc[i-1] - data['volume'].iloc[i]
        else:
            obv.iloc[i] = obv.iloc[i-1]
    
    return obv
