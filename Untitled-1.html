 <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{{ symbol }} - {{ strategy }} 回测报告</title>
            <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 1200px;
                    margin: 0 auto;
                    padding: 20px;
                }
                h1, h2, h3 {
                    color: #2c3e50;
                }
                .header {
                    text-align: center;
                    margin-bottom: 30px;
                    padding: 30px 0;
                    border-bottom: 1px solid #eee;
                    background: linear-gradient(to bottom, #f9f9f9, #ffffff);
                    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
                    border-radius: 5px;
                }
                .header h1 {
                    margin-bottom: 20px;
                    color: #2c3e50;
                    font-size: 2.2em;
                    font-weight: 700;
                    text-shadow: 1px 1px 1px rgba(0,0,0,0.1);
                }
                .report-meta {
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: center;
                    gap: 20px;
                    font-size: 0.95em;
                    color: #7f8c8d;
                }
                .report-meta p {
                    margin: 5px 0;
                }
                .section {
                    margin-bottom: 30px;
                    padding: 20px;
                    background-color: #f9f9f9;
                    border-radius: 5px;
                }
                .metrics-container {
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;
                }
                .metric-group {
                    flex: 0 0 30%;
                    margin-bottom: 20px;
                }
                .metric {
                    margin-bottom: 10px;
                    padding: 10px;
                    background-color: #fff;
                    border-radius: 3px;
                    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                }
                .metric-name {
                    font-weight: bold;
                    color: #7f8c8d;
                }
                .metric-value {
                    float: right;
                    font-weight: bold;
                    color: #2980b9;
                }
                .positive {
                    color: #27ae60;
                }
                .negative {
                    color: #e74c3c;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                    border-radius: 5px;
                    overflow: hidden;
                }
                th, td {
                    padding: 12px 15px;
                    text-align: left;
                    border-bottom: 1px solid #ddd;
                }
                th {
                    background-color: #2c3e50;
                    color: white;
                    font-weight: bold;
                    text-transform: uppercase;
                    font-size: 0.9em;
                }
                tr:hover {
                    background-color: #f5f5f5;
                }
                .table-success {
                    background-color: rgba(39, 174, 96, 0.1);
                }
                .table-danger {
                    background-color: rgba(231, 76, 60, 0.1);
                }
                .table-secondary {
                    background-color: rgba(189, 195, 199, 0.1);
                }
                .table-info {
                    background-color: rgba(52, 152, 219, 0.1);
                }
                tfoot {
                    font-weight: bold;
                    background-color: #f2f2f2;
                }
                .text-right {
                    text-align: right;
                }
                .section-description {
                    color: #7f8c8d;
                    font-style: italic;
                    margin-bottom: 20px;
                }
                .chart-container {
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;
                }
                .chart {
                    flex: 0 0 48%;
                    margin-bottom: 30px;
                    text-align: center;
                    background-color: #fff;
                    border-radius: 5px;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                    padding: 15px;
                    transition: transform 0.3s ease;
                }
                .chart:hover {
                    transform: translateY(-5px);
                    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                }
                .chart h3 {
                    margin-top: 0;
                    margin-bottom: 15px;
                    color: #2c3e50;
                    font-size: 1.2em;
                    border-bottom: 1px solid #eee;
                    padding-bottom: 10px;
                }
                .chart h3 small {
                    font-size: 0.75em;
                    color: #7f8c8d;
                    font-weight: normal;
                    margin-left: 5px;
                }
                .chart-box {
                    border-radius: 3px;
                    overflow: hidden;
                    background-color: #f9f9f9;
                }
                .chart img {
                    max-width: 100%;
                    height: auto;
                    border: 1px solid #ddd;
                    border-radius: 3px;
                }
                .footer {
                    text-align: center;
                    margin-top: 50px;
                    padding: 30px 0;
                    border-top: 1px solid #eee;
                    color: #7f8c8d;
                    font-size: 0.9em;
                    background-color: #f9f9f9;
                    border-radius: 0 0 5px 5px;
                }
                .footer p {
                    margin: 5px 0;
                }
                .footer .disclaimer {
                    font-size: 0.85em;
                    color: #999;
                    max-width: 600px;
                    margin: 10px auto;
                }
                .footer .copyright {
                    font-size: 0.9em;
                    margin-top: 15px;
                    font-weight: bold;
                    color: #666;
                }
                @media (max-width: 768px) {
                    .metric-group, .chart {
                        flex: 0 0 100%;
                    }
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>{{ symbol }} - {{ strategy }} 策略回测报告</h1>
                <script>
                    const reportData = {{ report_data | tojson }};
                    console.log(reportData); // For debugging
                </script>
                <div class="report-meta">
                    <p><strong>回测周期:</strong> {{ metrics.start_date|default('N/A', true) }} 至 {{ metrics.end_date|default('N/A', true) }}</p>
                    <p><strong>报告生成时间:</strong> {{ report_date }}</p>
                    <p><strong>初始资金:</strong> {{ metrics.initial_capital|default('10000', true)|int }}¥</p>
                </div>
            </div>

            <div class="section">
                <h2>绩效概览</h2>
                <p class="section-description">本节展示策略的核心绩效指标，包括收益、风险和交易相关的关键数据。</p>
                <div class="metrics-container">
                    <div class="metric-group">
                        <h3>收益指标</h3>
                        <div class="metric">
                            <span class="metric-name">总收益率</span>
                            <span class="metric-value {% if metrics.total_return > 0 %}positive{% else %}negative{% endif %}">{{ (metrics.total_return * 100)|round(2) }}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">年化收益率</span>
                            <span class="metric-value {% if metrics.annual_return > 0 %}positive{% else %}negative{% endif %}">{{ (metrics.annual_return * 100)|round(2) }}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">日均收益率</span>
                            <span class="metric-value {% if metrics.daily_return > 0 %}positive{% else %}negative{% endif %}">{{ (metrics.daily_return * 100)|round(4) }}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">最终净值</span>
                            <span class="metric-value {% if metrics.final_equity > metrics.initial_capital %}positive{% else %}negative{% endif %}">{{ metrics.final_equity|default(metrics.initial_capital * (1 + metrics.total_return))|round(2) }}¥</span>
                        </div>
                    </div>

                    <div class="metric-group">
                        <h3>风险指标</h3>
                        <div class="metric">
                            <span class="metric-name">最大回撤</span>
                            <span class="metric-value negative">{{ ((metrics.max_drawdown|default(0)) * 100)|round(2) }}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">年化波动率</span>
                            <span class="metric-value">{{ ((metrics.volatility_annual|default(0)) * 100)|round(2) }}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">夏普比率</span>
                            <span class="metric-value {% if metrics.sharpe_ratio|default(0) > 0 %}positive{% else %}negative{% endif %}">{{ (metrics.sharpe_ratio|default(0))|round(2) }}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">卡玛比率</span>
                            <span class="metric-value {% if metrics.calmar_ratio|default(0) > 0 %}positive{% else %}negative{% endif %}">{{ (metrics.calmar_ratio|default(0))|round(2) }}</span>
                        </div>
                    </div>

                    <div class="metric-group">
                        <h3>交易指标</h3>
                        <div class="metric">
                            <span class="metric-name">交易次数</span>
                            <span class="metric-value">{{ metrics.num_trades }}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">胜率</span>
                            <span class="metric-value {% if metrics.win_rate > 0.5 %}positive{% else %}negative{% endif %}">{{ (metrics.win_rate * 100)|round(2) }}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">盈亏比</span>
                            <span class="metric-value {% if metrics.profit_factor > 1 %}positive{% else %}negative{% endif %}">{{ metrics.profit_factor|round(2) }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>图表分析</h2>
                <p class="section-description">本节通过多种图表直观展示策略的表现，包括净值曲线、回撤分析、收益分布和交易特征等。</p>
                <div class="chart-container">
                    <div class="chart">
                        <h3>净值曲线 <small>策略资金变化趋势</small></h3>
                        <div id="equity-curve-chart" style="width: 100%;height:400px;" class="chart-box"></div>
                    </div>
                    <div class="chart">
                        <h3>回撤分析 <small>资金回撤情况</small></h3>
                        <div id="drawdowns-chart" style="width: 100%;height:400px;" class="chart-box"></div>
                    </div>
                    <div class="chart">
                        <h3>月度收益热力图 <small>月度表现分布</small></h3>
                        <div id="monthly-returns-heatmap-chart" style="width: 100%;height:400px;" class="chart-box"></div>
                    </div>
                    <div class="chart">
                        <h3>日收益分布 <small>日收益率频率分布</small></h3>
                        <div id="daily-returns-hist-chart" style="width: 100%;height:400px;" class="chart-box"></div>
                    </div>
                    {% if trades and trades|length > 0 %}
                    <div class="chart">
                        <h3>交易收益分布 <small>单笔交易收益率分布</small></h3>
                        <div id="trade-returns-hist-chart" style="width: 100%;height:400px;" class="chart-box"></div>
                    </div>
                    <div class="chart">
                        <h3>交易持续时间分布 <small>持仓周期分析</small></h3>
                        <div id="trade-duration-hist-chart" style="width: 100%;height:400px;" class="chart-box"></div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- 更新模板条件判断 -->
            <div class="section">
                <h2>交易明细记录</h2>
                <p class="section-description">本节展示所有交易的详细信息，包括入场/出场时间、价格、方向、持续时间及盈亏情况。</p>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>入场时间</th>
                                <th>出场时间</th>
                                <th>方向</th>
                                <th>持仓时长</th>
                                <th>入场价格</th>
                                <th>出场价格</th>
                                <th>数量</th>
                                <th>收益率(%)</th>
                                <th>盈亏(¥)</th>
                            </tr>
                        </thead>
                        <tbody>
                        {% if trades and trades|length > 0 %}
                            {% for trade in trades %}
                            <tr class="{% if trade.PnL > 0 %}table-success{% elif trade.PnL < 0 %}table-danger{% else %}table-secondary{% endif %}">
                                <td>{{ loop.index }}</td>
                                <td>{{ trade['Entry Timestamp']|default('-', true) }}</td>
                                <td>{{ trade['Exit Timestamp']|default('-', true) }}</td>
                                <td>{% if trade.Direction == 'Long' %}多头{% elif trade.Direction == 'Short' %}空头{% else %}{{ trade.Direction|default('-', true) }}{% endif %}</td>
                                <td>{{ trade.Duration|default('-', true) }}</td>
                                <td>{{ trade['Entry Price']|round(4)|default('-', true) }}</td>
                                <td>{{ trade['Exit Price']|round(4)|default('-', true) }}</td>
                                <td>{{ trade.Size|default('-', true) }}</td>
                                <td class="{% if trade.Return > 0 %}positive{% elif trade.Return < 0 %}negative{% endif %}">{{ (trade.Return * 100)|round(2)|default('-', true) }}</td>
                                <td class="{% if trade.PnL > 0 %}positive{% elif trade.PnL < 0 %}negative{% endif %}">{{ trade.PnL|round(2)|default('-', true) }}</td>
                            </tr>
                            {% endfor %}
                        {% else %}
                        <tr><td colspan="10" class="text-center">暂无有效交易记录</td></tr>
                        {% endif %}
                        </tbody>
                        {% if trades and trades|length > 0 %}
                        <tfoot>
                            <tr class="table-info">
                                <td colspan="8" class="text-right"><strong>总计:</strong></td>
                                <td class="{% if metrics.total_return > 0 %}positive{% elif metrics.total_return < 0 %}negative{% endif %}"><strong>{{ (metrics.total_return * 100)|round(2) }}</strong></td>
                                <td class="{% if metrics.net_profit > 0 %}positive{% elif metrics.net_profit < 0 %}negative{% endif %}"><strong>{{ metrics.net_profit|round(2) }}</strong></td>
                            </tr>
                        </tfoot>
                        {% endif %}
                    </table>
                </div>
            </div>

            <div class="section">
                <h2>详细指标分析</h2>
                <p class="section-description">本节展示策略的详细绩效指标，包括收益、风险和交易相关的各项数据。</p>
                <div class="metrics-container">
                    <div class="metric-group">
                        <h3>收益指标</h3>
                        {% for key, value in metrics.items() %}
                            {% if key in ['total_return', 'annual_return', 'daily_return', 'max_daily_profit', 'max_daily_loss'] %}
                            <div class="metric">
                                <span class="metric-name">
                                    {% if key == 'total_return' %}总收益率
                                    {% elif key == 'annual_return' %}年化收益率
                                    {% elif key == 'daily_return' %}日均收益率
                                    {% elif key == 'max_daily_profit' %}单日最大收益
                                    {% elif key == 'max_daily_loss' %}单日最大亏损
                                    {% else %}{{ key }}{% endif %}
                                </span>
                                <span class="metric-value {% if (value|default(0)) > 0 and key != 'max_daily_loss' %}positive{% elif (value|default(0)) < 0 or key == 'max_daily_loss' %}negative{% endif %}">
                                    {% if 'return' in key or 'drawdown' in key or 'daily' in key %}
                                        {{ ((value|default(0)) * 100)|round(2) }}%
                                    {% else %}
                                        {{ (value|default(0))|round(4) }}
                                    {% endif %}
                                </span>
                            </div>
                            {% endif %}
                        {% endfor %}
                    </div>

                    <div class="metric-group">
                        <h3>风险指标</h3>
                        {% for key, value in metrics.items() %}
                            {% if key in ['max_drawdown', 'max_drawdown_duration', 'volatility_annual', 'sharpe_ratio', 'sortino_ratio', 'calmar_ratio', 'omega_ratio'] %}
                            <div class="metric">
                                <span class="metric-name">
                                    {% if key == 'max_drawdown' %}最大回撤
                                    {% elif key == 'max_drawdown_duration' %}最长回撤期
                                    {% elif key == 'volatility_annual' %}年化波动率
                                    {% elif key == 'sharpe_ratio' %}夏普比率
                                    {% elif key == 'sortino_ratio' %}索提诺比率
                                    {% elif key == 'calmar_ratio' %}卡玛比率
                                    {% elif key == 'omega_ratio' %}欧米伽比率
                                    {% else %}{{ key }}{% endif %}
                                </span>
                                <span class="metric-value {% if key == 'max_drawdown' or key == 'max_drawdown_duration' %}negative{% elif (value|default(0)) > 0 %}positive{% else %}negative{% endif %}">
                                    {% if key == 'max_drawdown' or key == 'volatility_annual' %}
                                        {{ ((value|default(0)) * 100)|round(2) }}%
                                    {% elif key == 'max_drawdown_duration' %}
                                        {{ value|default('N/A') }}
                                    {% else %}
                                        {{ (value|default(0))|round(2) }}
                                    {% endif %}
                                </span>
                            </div>
                            {% endif %}
                        {% endfor %}
                    </div>

                    <div class="metric-group">
                        <h3>交易指标</h3>
                        {% for key, value in metrics.items() %}
                            {% if key in ['win_rate', 'profit_factor', 'expectancy', 'avg_win', 'avg_loss', 'best_trade', 'worst_trade', 'avg_trade', 'num_trades', 'max_win_streak', 'max_loss_streak'] %}
                            <div class="metric">
                                <span class="metric-name">
                                    {% if key == 'win_rate' %}胜率
                                    {% elif key == 'profit_factor' %}盈亏比
                                    {% elif key == 'expectancy' %}期望值
                                    {% elif key == 'avg_win' %}平均盈利
                                    {% elif key == 'avg_loss' %}平均亏损
                                    {% elif key == 'best_trade' %}最佳交易
                                    {% elif key == 'worst_trade' %}最差交易
                                    {% elif key == 'avg_trade' %}平均交易收益
                                    {% elif key == 'num_trades' %}交易次数
                                    {% elif key == 'max_win_streak' %}最长连胜
                                    {% elif key == 'max_loss_streak' %}最长连亏
                                    {% else %}{{ key }}{% endif %}
                                </span>
                                <span class="metric-value {% if key in ['win_rate', 'profit_factor', 'expectancy', 'avg_win', 'best_trade', 'max_win_streak'] and value > 0 %}positive{% elif key in ['avg_loss', 'worst_trade', 'max_loss_streak'] and value < 0 %}negative{% endif %}">
                                    {% if key == 'win_rate' %}
                                        {{ ((value|default(0)) * 100)|round(2) }}%
                                    {% elif key in ['num_trades', 'max_win_streak', 'max_loss_streak'] %}
                                        {{ (value|default(0))|int }}
                                    {% elif key in ['avg_win', 'avg_loss', 'best_trade', 'worst_trade', 'avg_trade'] %}
                                        {{ (value|default(0))|round(2) }}
                                    {% else %}
                                        {{ (value|default(0))|round(4) }}
                                    {% endif %}
                                </span>
                            </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>
            </div>

            <div class="footer">
                <p>由 QuantEngine 量化交易引擎自动生成 | {{ report_date }}</p>
                <p class="disclaimer">免责声明：本报告仅供参考，不构成任何投资建议。交易有风险，投资需谨慎。</p>
                <p class="copyright">© {{ report_date[:4] }} QuantEngine - 版权所有</p>
            </div>

            <!-- 引入ECharts库 -->
            <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>

            <script>
                // 等待文档加载完成
                document.addEventListener('DOMContentLoaded', function() {
                    // 获取图表容器
                    const equityCurveChartDom = document.getElementById('equity-curve-chart');
                    const drawdownsChartDom = document.getElementById('drawdowns-chart');
                    const monthlyReturnsHeatmapChartDom = document.getElementById('monthly-returns-heatmap-chart');
                    const dailyReturnsHistChartDom = document.getElementById('daily-returns-hist-chart');

                    // 检查容器是否存在
                    if (!equityCurveChartDom || !drawdownsChartDom || !monthlyReturnsHeatmapChartDom || !dailyReturnsHistChartDom) {
                        console.error('一个或多个图表容器不存在');
                        return;
                    }

                    // 初始化ECharts实例
                    const equityCurveChart = echarts.init(equityCurveChartDom);
                    const drawdownsChart = echarts.init(drawdownsChartDom);
                    const monthlyReturnsHeatmapChart = echarts.init(monthlyReturnsHeatmapChartDom);
                    const dailyReturnsHistChart = echarts.init(dailyReturnsHistChartDom);

                    // 可选的图表容器
                    const tradeReturnsHistChartDom = document.getElementById('trade-returns-hist-chart');
                    const tradeDurationHistChartDom = document.getElementById('trade-duration-hist-chart');

                    // 如果可选容器存在，则初始化
                    const tradeReturnsHistChart = tradeReturnsHistChartDom ? echarts.init(tradeReturnsHistChartDom) : null;
                    const tradeDurationHistChart = tradeDurationHistChartDom ? echarts.init(tradeDurationHistChartDom) : null;

                // 确保reportData和其子属性存在
                const performance = reportData.performance || {};
                const daily_values = performance.daily_values || {};
                const daily_returns = performance.daily_returns || {};

                // 净值曲线图表配置
                const equityCurveOption = {
                    title: {
                        text: '净值曲线'
                    },
                    tooltip: {
                        trigger: 'axis',
                        formatter: function (params) {
                            params = params[0];
                            const value = params.value || 0;
                            return params.name + '<br/>净值: ' + (typeof value.toFixed === 'function' ? value.toFixed(4) : '0.0000');
                        }
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: Object.keys(daily_values),
                        axisLabel: {
                            rotate: 45,
                            interval: Math.floor(Object.keys(daily_values).length / 10)
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: '净值',
                        scale: true
                    },
                    series: [
                        {
                            name: '净值',
                            type: 'line',
                            data: Object.values(reportData.performance?.daily_values || {}),
                            smooth: true,
                            showSymbol: false,
                            lineStyle: {
                                width: 2,
                                color: '#3498db'
                            },
                            areaStyle: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                    { offset: 0, color: 'rgba(52, 152, 219, 0.5)' },
                                    { offset: 1, color: 'rgba(52, 152, 219, 0.1)' }
                                ])
                            }
                        }
                    ]
                };

                // 回撤分析图表配置
                const drawdownsOption = {
                    title: {
                        text: '回撤分析'
                    },
                    tooltip: {
                        trigger: 'axis',
                        formatter: function (params) {
                            params = params[0];
                            return params.name + '<br/>回撤: ' + (params.value * 100).toFixed(2) + '%';
                        }
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: Object.keys(reportData.risk?.drawdowns || {}),
                        axisLabel: {
                            rotate: 45,
                            interval: Math.floor(Object.keys(reportData.risk?.drawdowns || {}).length / 10)
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: '回撤 (%)',
                        scale: true,
                        axisLabel: {
                            formatter: function (value) {
                                return (value * 100).toFixed(0) + '%';
                            }
                        },
                        inverse: true  // 反转Y轴，使回撤向下显示
                    },
                    series: [
                        {
                            name: '回撤',
                            type: 'line',
                            data: Object.values(reportData.risk?.drawdowns || {}),
                            smooth: true,
                            showSymbol: false,
                            lineStyle: {
                                width: 2,
                                color: '#e74c3c'
                            },
                            areaStyle: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                    { offset: 0, color: 'rgba(231, 76, 60, 0.1)' },
                                    { offset: 1, color: 'rgba(231, 76, 60, 0.5)' }
                                ])
                            }
                        }
                    ]
                };

                // 月度收益热力图配置
                // 处理月度收益数据
                let monthlyReturnsData = [];
                let years = [];
                const months = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'];

                // 确保advanced.monthly_returns_heatmap存在
                const advanced = reportData.advanced || {};
                const monthly_returns_heatmap = advanced.monthly_returns_heatmap || {};

                try {
                    if (Object.keys(monthly_returns_heatmap).length > 0) {
                        // 将数据转换为热力图所需格式
                        monthlyReturnsData = Object.entries(monthly_returns_heatmap).map(([date, value]) => {
                            try {
                                const dateParts = date.split('-');
                                const year = dateParts[0] || new Date().getFullYear().toString();
                                const month = dateParts[1] || '01';
                                const numValue = parseFloat(value) || 0;
                                return [year, month, (numValue * 100).toFixed(2)];
                            } catch (e) {
                                console.error('解析月度收益数据出错:', e);
                                return [new Date().getFullYear().toString(), '01', '0.00'];
                            }
                        });

                        // 提取年份列表
                        years = Array.from(new Set(monthlyReturnsData.map(item => item[0]))).sort();
                    } else {
                        // 如果没有数据，提供默认数据
                        const currentYear = new Date().getFullYear();
                        years = [currentYear.toString()];
                        // 为每个月创建默认数据
                        months.forEach(month => {
                            monthlyReturnsData.push([currentYear.toString(), month, '0.00']);
                        });
                    }
                } catch (e) {
                    console.error('处理月度收益热力图数据出错:', e);
                    // 提供默认数据
                    const currentYear = new Date().getFullYear();
                    years = [currentYear.toString()];
                    monthlyReturnsData = [];
                    months.forEach(month => {
                        monthlyReturnsData.push([currentYear.toString(), month, '0.00']);
                    });
                }

                const monthlyReturnsHeatmapOption = {
                    title: {
                        text: '月度收益热力图'
                    },
                    tooltip: {
                        position: 'top',
                        formatter: function (params) {
                            return params.value[0] + '-' + params.value[1] + ': ' + params.value[2] + '%';
                        }
                    },
                    grid: {
                        height: '50%',
                        top: '10%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: years,
                        splitArea: {
                            show: true
                        }
                    },
                    yAxis: {
                        type: 'category',
                        data: months,
                        splitArea: {
                            show: true
                        }
                    },
                    visualMap: {
                        min: -10,
                        max: 10,
                        calculable: true,
                        orient: 'horizontal',
                        left: 'center',
                        bottom: '15%',
                        inRange: {
                            color: ['#e74c3c', '#f9f9f9', '#27ae60'] // 红 -> 白 -> 绿
                        }
                    },
                    series: [
                        {
                            name: '月度收益',
                            type: 'heatmap',
                            data: monthlyReturnsData,
                            label: {
                                show: true,
                                formatter: function (params) {
                                    return params.value[2]; // 显示收益率
                                }
                            },
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            }
                        }
                    ]
                };

                // 日收益分布图表配置
                // 处理日收益数据
                let dailyReturnsData = [];

                // 确保advanced.daily_returns存在
                try {
                    if (advanced && advanced.daily_returns) {
                        // 将数据转换为直方图所需格式
                        dailyReturnsData = Object.values(advanced.daily_returns).map(value => {
                            const numValue = parseFloat(value) || 0;
                            return numValue * 100;
                        });
                    } else {
                        // 如果没有数据，提供默认数据
                        dailyReturnsData = [0, 0.1, -0.1, 0.2, -0.2, 0.15, -0.15];
                    }
                } catch (e) {
                    console.error('处理日收益数据出错:', e);
                    dailyReturnsData = [0, 0.1, -0.1, 0.2, -0.2, 0.15, -0.15]; // 提供默认数据
                }

                const dailyReturnsHistOption = {
                    title: {
                        text: '日收益分布'
                    },
                    tooltip: {
                        trigger: 'item',
                        formatter: function(params) {
                            const value = params.value || 0;
                            return '收益率: ' + (typeof value.toFixed === 'function' ? value.toFixed(2) : '0.00') + '%';
                        }
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'value',
                        name: '收益率 (%)',
                        axisLabel: {
                            formatter: '{value}%'
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: '频率'
                    },
                    series: [
                        {
                            name: '日收益',
                            type: 'histogram',
                            data: dailyReturnsData || [0], // 确保数据存在
                            emphasis: {
                                focus: 'series'
                            }
                        }
                    ]
                };

                // 交易收益分布图表配置 (示例，需要根据实际交易收益数据调整)
                // reportData.trades 是一个数组，每个元素是交易记录对象
                const trades = Array.isArray(reportData.trades) ? reportData.trades : [];
                const tradeReturnsData = trades.map(trade => trade && trade.return !== undefined ? trade.return * 100 : null).filter(value => value !== null);

                const tradeReturnsHistOption = {
                    title: {
                        text: '交易收益分布'
                    },
                    tooltip: {
                        trigger: 'axis',
                        formatter: function(params) {
                            const value = params[0].value || 0;
                            return '收益率: ' + (typeof value === 'number' ? value.toFixed(2) : '0.00') + '%';
                        }
                    },
                    xAxis: {
                        type: 'value',
                        name: '收益率 (%)'
                    },
                    yAxis: {
                        type: 'value',
                        name: '频率'
                    },
                    series: [
                        {
                            name: '交易收益',
                            type: 'histogram',
                            data: tradeReturnsData,
                            emphasis: {
                                focus: 'series'
                            }
                        }
                    ]
                };

                // 交易持续时间分布图表配置 (示例，需要根据实际交易持续时间数据调整)
                // reportData.trades 是一个数组，每个元素是交易记录对象
                // trade.duration 是字符串，需要解析
                const tradeDurationData = trades.map(trade => {
                    if (trade && trade.duration) {
                        try {
                            // 简单的解析，假设格式是 'X days HH:MM:SS' 或 'HH:MM:SS'
                            const parts = trade.duration.split(' ');
                            let days = 0;
                            let timeParts;
                            if (parts.length > 1) {
                                days = parseInt(parts[0] || '0');
                                timeParts = parts[2] ? parts[2].split(':') : (parts[1] ? parts[1].split(':') : ['0', '0', '0']);
                            } else {
                                timeParts = parts[0] ? parts[0].split(':') : ['0', '0', '0'];
                            }
                            const hours = parseInt(timeParts[0] || '0');
                            const minutes = parseInt(timeParts[1] || '0');
                            const seconds = parseInt(timeParts[2] || '0');
                            // 转换为总秒数或天数，这里转换为天数方便展示
                            return days + hours / 24 + minutes / (24 * 60) + seconds / (24 * 3600);
                        } catch (e) {
                            console.error('解析交易持续时间出错:', e);
                            return null;
                        }
                    }
                    return null;
                }).filter(value => value !== null);

                const tradeDurationHistOption = {
                    title: {
                        text: '交易持续时间分布'
                    },
                    tooltip: {
                        trigger: 'axis',
                        formatter: function(params) {
                            const value = params[0].value || 0;
                            return '持续时间: ' + (typeof value === 'number' ? value.toFixed(2) : '0.00') + ' 天';
                        }
                    },
                    xAxis: {
                        type: 'value',
                        name: '持续时间 (天)'
                    },
                    yAxis: {
                        type: 'value',
                        name: '频率'
                    },
                    series: [
                        {
                            name: '交易持续时间',
                            type: 'histogram',
                            data: tradeDurationData,
                            emphasis: {
                                focus: 'series'
                            }
                        }
                    ]
                };

                // 使用配置项和数据显示图表 - 增强版本
                // 检查图表容器是否存在，然后再渲染
                try {
                    if (document.getElementById('equity-curve-chart')) {
                        equityCurveChart.setOption(equityCurveOption);
                    }
                } catch (e) {
                    console.error('渲染净值曲线图表出错:', e);
                }

                // 检查是否有回撤数据再渲染
                try {
                    if (document.getElementById('drawdowns-chart')) {
                        drawdownsChart.setOption(drawdownsOption);
                    }
                } catch (e) {
                    console.error('渲染回撤分析图表出错:', e);
                }

                // 检查是否有月度收益数据再渲染
                try {
                    if (document.getElementById('monthly-returns-heatmap-chart') && monthlyReturnsData && monthlyReturnsData.length > 0) {
                        monthlyReturnsHeatmapChart.setOption(monthlyReturnsHeatmapOption);
                    }
                } catch (e) {
                    console.error('渲染月度收益热力图出错:', e);
                }

                // 检查是否有日收益数据再渲染
                try {
                    if (document.getElementById('daily-returns-hist-chart') && dailyReturnsData && dailyReturnsData.length > 0) {
                        dailyReturnsHistChart.setOption(dailyReturnsHistOption);
                    }
                } catch (e) {
                    console.error('渲染日收益分布图表出错:', e);
                }

                // 检查是否有交易收益数据再渲染
                try {
                    if (document.getElementById('trade-returns-hist-chart') && tradeReturnsData && tradeReturnsData.length > 0) {
                        tradeReturnsHistChart.setOption(tradeReturnsHistOption);
                    }
                } catch (e) {
                    console.error('渲染交易收益分布图表出错:', e);
                }

                // 检查是否有交易持续时间数据再渲染
                try {
                    if (document.getElementById('trade-duration-hist-chart') && tradeDurationData && tradeDurationData.length > 0) {
                        tradeDurationHistChart.setOption(tradeDurationHistOption);
                    }
                } catch (e) {
                    console.error('渲染交易持续时间分布图表出错:', e);
                }

                // 响应窗口大小变化 - 增强版本
                window.addEventListener('resize', function () {
                    // 只有在图表容器存在时才调整大小
                    try {
                        if (document.getElementById('equity-curve-chart')) {
                            equityCurveChart.resize();
                        }
                    } catch (e) {}

                    try {
                        if (document.getElementById('drawdowns-chart')) {
                            drawdownsChart.resize();
                        }
                    } catch (e) {}

                    try {
                        if (document.getElementById('monthly-returns-heatmap-chart')) {
                            monthlyReturnsHeatmapChart.resize();
                        }
                    } catch (e) {}

                    try {
                        if (document.getElementById('daily-returns-hist-chart')) {
                            dailyReturnsHistChart.resize();
                        }
                    } catch (e) {}

                    try {
                        if (document.getElementById('trade-returns-hist-chart') && tradeReturnsHistChart) {
                            tradeReturnsHistChart.resize();
                        }
                    } catch (e) {}

                    try {
                        if (document.getElementById('trade-duration-hist-chart') && tradeDurationHistChart) {
                            tradeDurationHistChart.resize();
                        }
                    } catch (e) {}
                });
            });
            </script>
        </body>
        </html>