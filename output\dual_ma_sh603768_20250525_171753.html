
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>sh603768 - dual_ma 回测报告</title>
            <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 1200px;
                    margin: 0 auto;
                    padding: 20px;
                }
                h1, h2, h3 {
                    color: #2c3e50;
                }
                .header {
                    text-align: center;
                    margin-bottom: 30px;
                    padding: 30px 0;
                    border-bottom: 1px solid #eee;
                    background: linear-gradient(to bottom, #f9f9f9, #ffffff);
                    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
                    border-radius: 5px;
                }
                .header h1 {
                    margin-bottom: 20px;
                    color: #2c3e50;
                    font-size: 2.2em;
                    font-weight: 700;
                    text-shadow: 1px 1px 1px rgba(0,0,0,0.1);
                }
                .report-meta {
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: center;
                    gap: 20px;
                    font-size: 0.95em;
                    color: #7f8c8d;
                }
                .report-meta p {
                    margin: 5px 0;
                }
                .section {
                    margin-bottom: 30px;
                    padding: 20px;
                    background-color: #f9f9f9;
                    border-radius: 5px;
                }
                .metrics-container {
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;
                }
                .metric-group {
                    flex: 0 0 30%;
                    margin-bottom: 20px;
                }
                .metric {
                    margin-bottom: 10px;
                    padding: 10px;
                    background-color: #fff;
                    border-radius: 3px;
                    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                }
                .metric-name {
                    font-weight: bold;
                    color: #7f8c8d;
                }
                .metric-value {
                    float: right;
                    font-weight: bold;
                    color: #2980b9;
                }
                .positive {
                    color: #27ae60;
                }
                .negative {
                    color: #e74c3c;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                    border-radius: 5px;
                    overflow: hidden;
                }
                th, td {
                    padding: 12px 15px;
                    text-align: left;
                    border-bottom: 1px solid #ddd;
                }
                th {
                    background-color: #2c3e50;
                    color: white;
                    font-weight: bold;
                    text-transform: uppercase;
                    font-size: 0.9em;
                }
                tr:hover {
                    background-color: #f5f5f5;
                }
                .table-success {
                    background-color: rgba(39, 174, 96, 0.1);
                }
                .table-danger {
                    background-color: rgba(231, 76, 60, 0.1);
                }
                .table-secondary {
                    background-color: rgba(189, 195, 199, 0.1);
                }
                .table-info {
                    background-color: rgba(52, 152, 219, 0.1);
                }
                tfoot {
                    font-weight: bold;
                    background-color: #f2f2f2;
                }
                .text-right {
                    text-align: right;
                }
                .section-description {
                    color: #7f8c8d;
                    font-style: italic;
                    margin-bottom: 20px;
                }
                .chart-container {
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;
                }
                .chart {
                    flex: 0 0 48%;
                    margin-bottom: 30px;
                    text-align: center;
                    background-color: #fff;
                    border-radius: 5px;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                    padding: 15px;
                    transition: transform 0.3s ease;
                }
                .chart:hover {
                    transform: translateY(-5px);
                    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                }
                .chart h3 {
                    margin-top: 0;
                    margin-bottom: 15px;
                    color: #2c3e50;
                    font-size: 1.2em;
                    border-bottom: 1px solid #eee;
                    padding-bottom: 10px;
                }
                .chart h3 small {
                    font-size: 0.75em;
                    color: #7f8c8d;
                    font-weight: normal;
                    margin-left: 5px;
                }
                .chart-box {
                    border-radius: 3px;
                    overflow: hidden;
                    background-color: #f9f9f9;
                }
                .chart img {
                    max-width: 100%;
                    height: auto;
                    border: 1px solid #ddd;
                    border-radius: 3px;
                }
                .footer {
                    text-align: center;
                    margin-top: 50px;
                    padding: 30px 0;
                    border-top: 1px solid #eee;
                    color: #7f8c8d;
                    font-size: 0.9em;
                    background-color: #f9f9f9;
                    border-radius: 0 0 5px 5px;
                }
                .footer p {
                    margin: 5px 0;
                }
                .footer .disclaimer {
                    font-size: 0.85em;
                    color: #999;
                    max-width: 600px;
                    margin: 10px auto;
                }
                .footer .copyright {
                    font-size: 0.9em;
                    margin-top: 15px;
                    font-weight: bold;
                    color: #666;
                }
                @media (max-width: 768px) {
                    .metric-group, .chart {
                        flex: 0 0 100%;
                    }
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>sh603768 - dual_ma 策略回测报告</h1>
                <script>
                    const reportData = {"annualized_return": 0.5741471364267343, "chart_data": {"advanced": {"daily_returns": {"daily_return": {"2020-01-02 00:00:00": 0.0, "2020-01-03 00:00:00": 0.0, "2020-01-06 00:00:00": 0.0, "2020-01-07 00:00:00": 0.0, "2020-01-08 00:00:00": 0.0, "2020-01-09 00:00:00": 0.0, "2020-01-10 00:00:00": 0.0, "2020-01-13 00:00:00": 0.0, "2020-01-14 00:00:00": 0.0, "2020-01-15 00:00:00": 0.0, "2020-01-16 00:00:00": 0.0, "2020-01-17 00:00:00": 0.0, "2020-01-20 00:00:00": 0.0, "2020-01-21 00:00:00": 0.0, "2020-01-22 00:00:00": 0.0, "2020-01-23 00:00:00": 0.0, "2020-02-03 00:00:00": 0.0, "2020-02-04 00:00:00": 0.0, "2020-02-05 00:00:00": 0.0, "2020-02-06 00:00:00": 0.0, "2020-02-07 00:00:00": 0.0, "2020-02-10 00:00:00": 0.0, "2020-02-11 00:00:00": 0.0, "2020-02-12 00:00:00": 0.0, "2020-02-13 00:00:00": 0.0, "2020-02-14 00:00:00": 0.0, "2020-02-17 00:00:00": 0.0, "2020-02-18 00:00:00": 0.0, "2020-02-19 00:00:00": 0.0, "2020-02-20 00:00:00": 0.0, "2020-02-21 00:00:00": -0.0012986114155761606, "2020-02-24 00:00:00": 0.006122448979591756, "2020-02-25 00:00:00": 0.014198782961460404, "2020-02-26 00:00:00": -0.017999999999999905, "2020-02-27 00:00:00": 0.0030549898167004977, "2020-02-28 00:00:00": -0.05888324873096442, "2020-03-02 00:00:00": 0.04962243797195265, "2020-03-03 00:00:00": -0.005138746145940542, "2020-03-04 00:00:00": 0.008264462809917394, "2020-03-05 00:00:00": 0.014344262295082072, "2020-03-06 00:00:00": 0.003030303030302918, "2020-03-09 00:00:00": -0.03222557905337362, "2020-03-10 00:00:00": 0.012486992715921049, "2020-03-11 00:00:00": 0.0, "2020-03-12 00:00:00": -0.01977514234326833, "2020-03-13 00:00:00": 0.0, "2020-03-16 00:00:00": 0.0, "2020-03-17 00:00:00": 0.0, "2020-03-18 00:00:00": 0.0, "2020-03-19 00:00:00": 0.0, "2020-03-20 00:00:00": 0.0, "2020-03-23 00:00:00": 0.0, "2020-03-24 00:00:00": 0.0, "2020-03-25 00:00:00": 0.0, "2020-03-26 00:00:00": 0.0, "2020-03-27 00:00:00": 0.0, "2020-03-30 00:00:00": 0.0, "2020-03-31 00:00:00": 0.0, "2020-04-01 00:00:00": 0.0, "2020-04-02 00:00:00": 0.0, "2020-04-03 00:00:00": 0.0, "2020-04-07 00:00:00": 0.0, "2020-04-08 00:00:00": 0.0, "2020-04-09 00:00:00": 0.0, "2020-04-10 00:00:00": 0.0, "2020-04-13 00:00:00": -0.0012986114155760186, "2020-04-14 00:00:00": 0.008800880088008823, "2020-04-15 00:00:00": -0.0032715376226826504, "2020-04-16 00:00:00": 0.0010940919037198593, "2020-04-17 00:00:00": -0.008743169398907126, "2020-04-20 00:00:00": 0.0187431091510474, "2020-04-21 00:00:00": -0.01082251082251074, "2020-04-22 00:00:00": 0.015317286652078627, "2020-04-23 00:00:00": 0.011853448275862228, "2020-04-24 00:00:00": 0.0021299254526090552, "2020-04-27 00:00:00": 0.002125398512221083, "2020-04-28 00:00:00": 0.0021208907741250295, "2020-04-29 00:00:00": 0.01375661375661381, "2020-04-30 00:00:00": 0.0, "2020-05-06 00:00:00": 0.02713987473903963, "2020-05-07 00:00:00": -0.009146341463414606, "2020-05-08 00:00:00": 0.013333333333333525, "2020-05-11 00:00:00": 0.02834008097165978, "2020-05-12 00:00:00": 0.0, "2020-05-13 00:00:00": -0.014763779527559143, "2020-05-14 00:00:00": 0.002997002997002988, "2020-05-15 00:00:00": -0.011952191235059724, "2020-05-18 00:00:00": -0.029233870967741753, "2020-05-19 00:00:00": 0.010384215991692546, "2020-05-20 00:00:00": -0.0010277492291880283, "2020-05-21 00:00:00": 0.024691358024691277, "2020-05-22 00:00:00": 0.016064257028112355, "2020-05-25 00:00:00": 0.01877470355731242, "2020-05-26 00:00:00": 0.0019398642095052403, "2020-05-27 00:00:00": 0.013552758954501453, "2020-05-28 00:00:00": -0.0019102196752626927, "2020-05-29 00:00:00": -0.010526315789473694, "2020-06-01 00:00:00": 0.04061895551257254, "2020-06-02 00:00:00": -0.01301115241635688, "2020-06-03 00:00:00": -0.00941619585687388, "2020-06-04 00:00:00": 0.013307984790874658, "2020-06-05 00:00:00": -0.0009380863039399169, "2020-06-08 00:00:00": 0.0, "2020-06-09 00:00:00": 0.032863849765258156, "2020-06-10 00:00:00": 0.016363636363636313, "2020-06-11 00:00:00": -0.002683363148479419, "2020-06-12 00:00:00": 0.022421524663677143, "2020-06-15 00:00:00": 0.023684210526315835, "2020-06-16 00:00:00": -0.01199657240788358, "2020-06-17 00:00:00": -0.0008673026886382928, "2020-06-18 00:00:00": 0.017361111111111216, "2020-06-19 00:00:00": 0.011945392491467461, "2020-06-22 00:00:00": 0.016863406408094538, "2020-06-23 00:00:00": 0.04311774461028186, "2020-06-24 00:00:00": 0.008744038155802762, "2020-06-29 00:00:00": -0.018124507486209487, "2020-06-30 00:00:00": 0.056982343499197396, "2020-07-01 00:00:00": 0.01822323462414573, "2020-07-02 00:00:00": 0.03504847129008226, "2020-07-03 00:00:00": -0.018731988472622745, "2020-07-06 00:00:00": 0.090308370044053, "2020-07-07 00:00:00": 0.08484848484848477, "2020-07-08 00:00:00": -0.01613904407200486, "2020-07-09 00:00:00": -0.006940063091482742, "2020-07-10 00:00:00": -0.010800508259212107, "2020-07-13 00:00:00": 0.06422607578676946, "2020-07-14 00:00:00": -0.015087507543753778, "2020-07-15 00:00:00": 0.022671568627450966, "2020-07-16 00:00:00": -0.024565608148592002, "2020-07-17 00:00:00": -0.05589680589680594, "2020-07-20 00:00:00": 0.03448275862068967, "2020-07-21 00:00:00": 0.025157232704402666, "2020-07-22 00:00:00": -0.028220858895705653, "2020-07-23 00:00:00": -0.006313131313131351, "2020-07-24 00:00:00": -0.03557814485387548, "2020-07-27 00:00:00": 0.07641633728590257, "2020-07-28 00:00:00": -0.002447980416156552, "2020-07-29 00:00:00": 0.0067484662576686345, "2020-07-30 00:00:00": -0.013406459475929325, "2020-07-31 00:00:00": -0.01605929586164305, "2020-08-03 00:00:00": 0.030131826741996312, "2020-08-04 00:00:00": -0.024984765386959203, "2020-08-05 00:00:00": -0.0024999999999998786, "2020-08-06 00:00:00": 0.0764411027568921, "2020-08-07 00:00:00": -0.009313154831199093, "2020-08-10 00:00:00": -0.05816686251468858, "2020-08-11 00:00:00": -0.014971927635683217, "2020-08-12 00:00:00": 0.05256491450285004, "2020-08-13 00:00:00": 0.05776173285198554, "2020-08-14 00:00:00": -0.011376564277588236, "2020-08-17 00:00:00": 0.001726121979286688, "2020-08-18 00:00:00": 0.05514072372199883, "2020-08-19 00:00:00": -0.07729994556341872, "2020-08-20 00:00:00": 0.0023598820058997514, "2020-08-21 00:00:00": 0.01942319011183059, "2020-08-24 00:00:00": 0.01212471131639719, "2020-08-25 00:00:00": -0.00798630918425564, "2020-08-26 00:00:00": -0.003450258769407695, "2020-08-27 00:00:00": 0.05424120023081366, "2020-08-28 00:00:00": 0.03940886699507377, "2020-08-31 00:00:00": 0.006319115323854785, "2020-09-01 00:00:00": 0.010465724751438957, "2020-09-02 00:00:00": -0.0414293112377005, "2020-09-03 00:00:00": -0.058346839546191354, "2020-09-04 00:00:00": -0.013769363166953485, "2020-09-07 00:00:00": -0.016870273414776083, "2020-09-08 00:00:00": 0.012292079230769115, "2020-09-09 00:00:00": 0.0, "2020-09-10 00:00:00": 0.0, "2020-09-11 00:00:00": 0.0, "2020-09-14 00:00:00": 0.0, "2020-09-15 00:00:00": 0.0, "2020-09-16 00:00:00": 0.0, "2020-09-17 00:00:00": 0.0, "2020-09-18 00:00:00": 0.0, "2020-09-21 00:00:00": 0.0, "2020-09-22 00:00:00": 0.0, "2020-09-23 00:00:00": 0.0, "2020-09-24 00:00:00": 0.0, "2020-09-25 00:00:00": 0.0, "2020-09-28 00:00:00": 0.0, "2020-09-29 00:00:00": 0.0, "2020-09-30 00:00:00": 0.0, "2020-10-09 00:00:00": 0.0, "2020-10-12 00:00:00": 0.0, "2020-10-13 00:00:00": 0.0, "2020-10-14 00:00:00": -0.0012986114155761476, "2020-10-15 00:00:00": -0.08723281340265736, "2020-10-16 00:00:00": -0.058187691772152085, "2020-10-19 00:00:00": 0.0, "2020-10-20 00:00:00": 0.0, "2020-10-21 00:00:00": 0.0, "2020-10-22 00:00:00": 0.0, "2020-10-23 00:00:00": 0.0, "2020-10-26 00:00:00": 0.0, "2020-10-27 00:00:00": 0.0, "2020-10-28 00:00:00": 0.0, "2020-10-29 00:00:00": 0.0, "2020-10-30 00:00:00": -0.0012986114155762924, "2020-11-02 00:00:00": -0.018541409147095265, "2020-11-03 00:00:00": 0.051637279596977385, "2020-11-04 00:00:00": 0.01976047904191617, "2020-11-05 00:00:00": -0.0023487962419262006, "2020-11-06 00:00:00": -0.010594467333725596, "2020-11-09 00:00:00": 0.015466983938132138, "2020-11-10 00:00:00": 0.008201523140011754, "2020-11-11 00:00:00": -0.0290528762347472, "2020-11-12 00:00:00": 0.014362657091561825, "2020-11-13 00:00:00": 0.002359882005899539, "2020-11-16 00:00:00": 0.013537374926427464, "2020-11-17 00:00:00": 0.0, "2020-11-18 00:00:00": 0.011614401858304353, "2020-11-19 00:00:00": 0.006888633754305255, "2020-11-20 00:00:00": 0.013683010262257933, "2020-11-23 00:00:00": 0.019122609673790696, "2020-11-24 00:00:00": -0.01821192052980149, "2020-11-25 00:00:00": 0.0, "2020-11-26 00:00:00": 0.0, "2020-11-27 00:00:00": -0.026981450252951057, "2020-11-30 00:00:00": -0.05430387059503169, "2020-12-01 00:00:00": -0.03485407782529019, "2020-12-02 00:00:00": 0.0, "2020-12-03 00:00:00": 0.0, "2020-12-04 00:00:00": 0.0, "2020-12-07 00:00:00": 0.0, "2020-12-08 00:00:00": 0.0, "2020-12-09 00:00:00": 0.0, "2020-12-10 00:00:00": 0.0, "2020-12-11 00:00:00": 0.0, "2020-12-14 00:00:00": 0.0, "2020-12-15 00:00:00": 0.0, "2020-12-16 00:00:00": 0.0, "2020-12-17 00:00:00": 0.0, "2020-12-18 00:00:00": 0.0, "2020-12-21 00:00:00": 0.0, "2020-12-22 00:00:00": 0.0, "2020-12-23 00:00:00": 0.0, "2020-12-24 00:00:00": 0.0, "2020-12-25 00:00:00": -0.0012986114155761886, "2020-12-28 00:00:00": -0.019907100199071045, "2020-12-29 00:00:00": -0.015572105619499064, "2020-12-30 00:00:00": 0.028198074277854188, "2020-12-31 00:00:00": 0.03745819397993322}}, "monthly_returns_heatmap": {"monthly_return": {"2020-01-31 00:00:00": 0.0, "2020-02-29 00:00:00": -0.05531001304310135, "2020-03-31 00:00:00": 0.028866004854368743, "2020-04-30 00:00:00": 0.05253677696796255, "2020-05-31 00:00:00": 0.07933194154488543, "2020-06-30 00:00:00": 0.27369439071566704, "2020-07-31 00:00:00": 0.20956719817767677, "2020-08-31 00:00:00": 0.19962335216572558, "2020-09-30 00:00:00": -0.10477571224489812, "2020-10-31 00:00:00": -0.14257589595669662, "2020-11-30 00:00:00": 0.011742892459826626, "2020-12-31 00:00:00": -0.007964578500662345}}, "trade_returns_distribution": {"negative": 3, "positive": 2, "values": [-0.028048999469917794, 0.8798590201877331, -0.14150342569042731, -0.024795977076075948, 0.027868911380590326]}}, "performance": {"cumulative_returns": {"2020-01-02 00:00:00": 0.0, "2020-01-03 00:00:00": 0.0, "2020-01-06 00:00:00": 0.0, "2020-01-07 00:00:00": 0.0, "2020-01-08 00:00:00": 0.0, "2020-01-09 00:00:00": 0.0, "2020-01-10 00:00:00": 0.0, "2020-01-13 00:00:00": 0.0, "2020-01-14 00:00:00": 0.0, "2020-01-15 00:00:00": 0.0, "2020-01-16 00:00:00": 0.0, "2020-01-17 00:00:00": 0.0, "2020-01-20 00:00:00": 0.0, "2020-01-21 00:00:00": 0.0, "2020-01-22 00:00:00": 0.0, "2020-01-23 00:00:00": 0.0, "2020-02-03 00:00:00": 0.0, "2020-02-04 00:00:00": 0.0, "2020-02-05 00:00:00": 0.0, "2020-02-06 00:00:00": 0.0, "2020-02-07 00:00:00": 0.0, "2020-02-10 00:00:00": 0.0, "2020-02-11 00:00:00": 0.0, "2020-02-12 00:00:00": 0.0, "2020-02-13 00:00:00": 0.0, "2020-02-14 00:00:00": 0.0, "2020-02-17 00:00:00": 0.0, "2020-02-18 00:00:00": 0.0, "2020-02-19 00:00:00": 0.0, "2020-02-20 00:00:00": 0.0, "2020-02-21 00:00:00": -0.0012986114155761896, "2020-02-24 00:00:00": 0.004815886881879283, "2020-02-25 00:00:00": 0.019083049575942423, "2020-02-26 00:00:00": 0.0007395546835755606, "2020-02-27 00:00:00": 0.003796803832303297, "2020-02-28 00:00:00": -0.05531001304310135, "2020-03-02 00:00:00": -0.00843219276260776, "2020-03-03 00:00:00": -0.01352760801048769, "2020-03-04 00:00:00": -0.005374943613880134, "2020-03-05 00:00:00": 0.008892219080183006, "2020-03-06 00:00:00": 0.011949468228910742, "2020-03-09 00:00:00": -0.020661189357519483, "2020-03-10 00:00:00": -0.008432192762608093, "2020-03-11 00:00:00": -0.008432192762608093, "2020-03-12 00:00:00": -0.028040587293730046, "2020-03-13 00:00:00": -0.028040587293730046, "2020-03-16 00:00:00": -0.028040587293730046, "2020-03-17 00:00:00": -0.028040587293730046, "2020-03-18 00:00:00": -0.028040587293730046, "2020-03-19 00:00:00": -0.028040587293730046, "2020-03-20 00:00:00": -0.028040587293730046, "2020-03-23 00:00:00": -0.028040587293730046, "2020-03-24 00:00:00": -0.028040587293730046, "2020-03-25 00:00:00": -0.028040587293730046, "2020-03-26 00:00:00": -0.028040587293730046, "2020-03-27 00:00:00": -0.028040587293730046, "2020-03-30 00:00:00": -0.028040587293730046, "2020-03-31 00:00:00": -0.028040587293730046, "2020-04-01 00:00:00": -0.028040587293730046, "2020-04-02 00:00:00": -0.028040587293730046, "2020-04-03 00:00:00": -0.028040587293730046, "2020-04-07 00:00:00": -0.028040587293730046, "2020-04-08 00:00:00": -0.028040587293730046, "2020-04-09 00:00:00": -0.028040587293730046, "2020-04-10 00:00:00": -0.028040587293730046, "2020-04-13 00:00:00": -0.029302784882546895, "2020-04-14 00:00:00": -0.02075979509053405, "2020-04-15 00:00:00": -0.02396341626253884, "2020-04-16 00:00:00": -0.022895542538537206, "2020-04-17 00:00:00": -0.03143853233055005, "2020-04-20 00:00:00": -0.013284679022522838, "2020-04-21 00:00:00": -0.023963416262538728, "2020-04-22 00:00:00": -0.009013184126516527, "2020-04-23 00:00:00": 0.0027334268375012183, "2020-04-24 00:00:00": 0.004869174285504263, "2020-04-27 00:00:00": 0.00700492173350753, "2020-04-28 00:00:00": 0.009140669181510575, "2020-04-29 00:00:00": 0.023023027593531475, "2020-04-30 00:00:00": 0.023023027593531475, "2020-05-06 00:00:00": 0.050787744417573055, "2020-05-07 00:00:00": 0.04117688090155869, "2020-05-08 00:00:00": 0.05505923931357959, "2020-05-11 00:00:00": 0.08495970358562421, "2020-05-12 00:00:00": 0.08495970358562421, "2020-05-13 00:00:00": 0.06894159772560005, "2020-05-14 00:00:00": 0.07214521889760483, "2020-05-15 00:00:00": 0.05933073420958568, "2020-05-18 00:00:00": 0.02836239621353953, "2020-05-19 00:00:00": 0.03904113345355542, "2020-05-20 00:00:00": 0.0379732597295539, "2020-05-21 00:00:00": 0.06360222910559221, "2020-05-22 00:00:00": 0.08068820868961768, "2020-05-25 00:00:00": 0.10097780944564838, "2020-05-26 00:00:00": 0.10311355689365143, "2020-05-27 00:00:00": 0.11806378902967385, "2020-05-28 00:00:00": 0.11592804158167058, "2020-05-29 00:00:00": 0.10418143061765295, "2020-06-01 00:00:00": 0.14903212702572, "2020-06-02 00:00:00": 0.13408189488969757, "2020-06-03 00:00:00": 0.12340315764968146, "2020-06-04 00:00:00": 0.13835338978570388, "2020-06-05 00:00:00": 0.13728551606170236, "2020-06-08 00:00:00": 0.13728551606170236, "2020-06-09 00:00:00": 0.1746610964017583, "2020-06-10 00:00:00": 0.19388282343378704, "2020-06-11 00:00:00": 0.19067920226178225, "2020-06-12 00:00:00": 0.2173760453618223, "2020-06-15 00:00:00": 0.24620863590986541, "2020-06-16 00:00:00": 0.231258403773843, "2020-06-17 00:00:00": 0.23019053004984147, "2020-06-18 00:00:00": 0.25154800452987347, "2020-06-19 00:00:00": 0.26649823666589567, "2020-06-22 00:00:00": 0.2878557111459279, "2020-06-23 00:00:00": 0.34338514479401105, "2020-06-24 00:00:00": 0.3551317557580287, "2020-06-29 00:00:00": 0.3305706601059921, "2020-06-30 00:00:00": 0.40638969451010554, "2020-07-01 00:00:00": 0.43201866388614385, "2020-07-02 00:00:00": 0.4822087289142194, "2020-07-03 00:00:00": 0.4544440120901776, "2020-07-06 00:00:00": 0.5857924801423744, "2020-07-07 00:00:00": 0.7203445693665758, "2020-07-08 00:00:00": 0.6925798525425344, "2020-07-09 00:00:00": 0.6808332415785165, "2020-07-10 00:00:00": 0.6626793882704896, "2020-07-13 00:00:00": 0.7694667606706493, "2020-07-14 00:00:00": 0.7427699175706093, "2020-07-15 00:00:00": 0.7822812453586685, "2020-07-16 00:00:00": 0.738498422674603, "2020-07-17 00:00:00": 0.6413219137904576, "2020-07-20 00:00:00": 0.6979192211625425, "2020-07-21 00:00:00": 0.7406341701226067, "2020-07-22 00:00:00": 0.6915119788185329, "2020-07-23 00:00:00": 0.6808332415785168, "2020-07-24 00:00:00": 0.6210323130344273, "2020-07-27 00:00:00": 0.744905665018613, "2020-07-28 00:00:00": 0.7406341701226067, "2020-07-29 00:00:00": 0.7523807810866243, "2020-07-30 00:00:00": 0.728887559158589, "2020-07-31 00:00:00": 0.7011228423345475, "2020-08-03 00:00:00": 0.7523807810866243, "2020-08-04 00:00:00": 0.7085979584025588, "2020-08-05 00:00:00": 0.7043264635065527, "2020-08-06 00:00:00": 0.8346070578347475, "2020-08-07 00:00:00": 0.8175210782507218, "2020-08-10 00:00:00": 0.7118015795745636, "2020-08-11 00:00:00": 0.686172610198525, "2020-08-12 00:00:00": 0.7748061292906578, "2020-08-13 00:00:00": 0.8773220067948113, "2020-08-14 00:00:00": 0.8559645323147791, "2020-08-17 00:00:00": 0.8591681534867843, "2020-08-18 00:00:00": 0.9616840309909376, "2020-08-19 00:00:00": 0.8100459621827103, "2020-08-20 00:00:00": 0.8143174570787166, "2020-08-21 00:00:00": 0.8495572899707697, "2020-08-24 00:00:00": 0.8719826381748033, "2020-08-25 00:00:00": 0.8570324060387806, "2020-08-26 00:00:00": 0.850625163694771, "2020-08-27 00:00:00": 0.9510052937509215, "2020-08-28 00:00:00": 1.0278922018790366, "2020-08-31 00:00:00": 1.0407066865670562, "2020-09-01 00:00:00": 1.062064161047088, "2020-09-02 00:00:00": 0.9766342631269602, "2020-09-03 00:00:00": 0.8613039009347874, "2020-09-04 00:00:00": 0.8356749315587491, "2020-09-07 00:00:00": 0.8047065935627025, "2020-09-08 00:00:00": 0.8268901899990664, "2020-09-09 00:00:00": 0.8268901899990664, "2020-09-10 00:00:00": 0.8268901899990664, "2020-09-11 00:00:00": 0.8268901899990664, "2020-09-14 00:00:00": 0.8268901899990664, "2020-09-15 00:00:00": 0.8268901899990664, "2020-09-16 00:00:00": 0.8268901899990664, "2020-09-17 00:00:00": 0.8268901899990664, "2020-09-18 00:00:00": 0.8268901899990664, "2020-09-21 00:00:00": 0.8268901899990664, "2020-09-22 00:00:00": 0.8268901899990664, "2020-09-23 00:00:00": 0.8268901899990664, "2020-09-24 00:00:00": 0.8268901899990664, "2020-09-25 00:00:00": 0.8268901899990664, "2020-09-28 00:00:00": 0.8268901899990664, "2020-09-29 00:00:00": 0.8268901899990664, "2020-09-30 00:00:00": 0.8268901899990664, "2020-10-09 00:00:00": 0.8268901899990664, "2020-10-12 00:00:00": 0.8268901899990664, "2020-10-13 00:00:00": 0.8268901899990664, "2020-10-14 00:00:00": 0.8245177695433294, "2020-10-15 00:00:00": 0.6653599514029236, "2020-10-16 00:00:00": 0.568456499861004, "2020-10-19 00:00:00": 0.568456499861004, "2020-10-20 00:00:00": 0.568456499861004, "2020-10-21 00:00:00": 0.568456499861004, "2020-10-22 00:00:00": 0.568456499861004, "2020-10-23 00:00:00": 0.568456499861004, "2020-10-26 00:00:00": 0.568456499861004, "2020-10-27 00:00:00": 0.568456499861004, "2020-10-28 00:00:00": 0.568456499861004, "2020-10-29 00:00:00": 0.568456499861004, "2020-10-30 00:00:00": 0.5664196843454496, "2020-11-02 00:00:00": 0.537376056081937, "2020-11-03 00:00:00": 0.6167619733355381, "2020-11-04 00:00:00": 0.648709964425402, "2020-11-05 00:00:00": 0.6448374806569332, "2020-11-06 00:00:00": 0.6274113036988258, "2020-11-09 00:00:00": 0.65258244819387, "2020-11-10 00:00:00": 0.6661361413835092, "2020-11-11 00:00:00": 0.6177300942776549, "2020-11-12 00:00:00": 0.6409649968884648, "2020-11-13 00:00:00": 0.644837480656933, "2020-11-16 00:00:00": 0.6671042623256263, "2020-11-17 00:00:00": 0.6671042623256263, "2020-11-18 00:00:00": 0.6864666811679683, "2020-11-19 00:00:00": 0.6980841324733731, "2020-11-20 00:00:00": 0.7213190350841838, "2020-11-23 00:00:00": 0.7542351471161646, "2020-11-24 00:00:00": 0.7222871560263004, "2020-11-25 00:00:00": 0.7222871560263004, "2020-11-26 00:00:00": 0.7222871560263004, "2020-11-27 00:00:00": 0.6758173508046803, "2020-11-30 00:00:00": 0.5848139822456742, "2020-12-01 00:00:00": 0.5295767523698753, "2020-12-02 00:00:00": 0.5295767523698753, "2020-12-03 00:00:00": 0.5295767523698753, "2020-12-04 00:00:00": 0.5295767523698753, "2020-12-07 00:00:00": 0.5295767523698753, "2020-12-08 00:00:00": 0.5295767523698753, "2020-12-09 00:00:00": 0.5295767523698753, "2020-12-10 00:00:00": 0.5295767523698753, "2020-12-11 00:00:00": 0.5295767523698753, "2020-12-14 00:00:00": 0.5295767523698753, "2020-12-15 00:00:00": 0.5295767523698753, "2020-12-16 00:00:00": 0.5295767523698753, "2020-12-17 00:00:00": 0.5295767523698753, "2020-12-18 00:00:00": 0.5295767523698753, "2020-12-21 00:00:00": 0.5295767523698753, "2020-12-22 00:00:00": 0.5295767523698753, "2020-12-23 00:00:00": 0.5295767523698753, "2020-12-24 00:00:00": 0.5295767523698753, "2020-12-25 00:00:00": 0.5275904265382478, "2020-12-28 00:00:00": 0.49718053085400915, "2020-12-29 00:00:00": 0.4738662774960929, "2020-12-30 00:00:00": 0.5154264682645522, "2020-12-31 00:00:00": 0.5721916068751309}, "daily_values": {"2020-01-02 00:00:00": 100000.0, "2020-01-03 00:00:00": 100000.0, "2020-01-06 00:00:00": 100000.0, "2020-01-07 00:00:00": 100000.0, "2020-01-08 00:00:00": 100000.0, "2020-01-09 00:00:00": 100000.0, "2020-01-10 00:00:00": 100000.0, "2020-01-13 00:00:00": 100000.0, "2020-01-14 00:00:00": 100000.0, "2020-01-15 00:00:00": 100000.0, "2020-01-16 00:00:00": 100000.0, "2020-01-17 00:00:00": 100000.0, "2020-01-20 00:00:00": 100000.0, "2020-01-21 00:00:00": 100000.0, "2020-01-22 00:00:00": 100000.0, "2020-01-23 00:00:00": 100000.0, "2020-02-03 00:00:00": 100000.0, "2020-02-04 00:00:00": 100000.0, "2020-02-05 00:00:00": 100000.0, "2020-02-06 00:00:00": 100000.0, "2020-02-07 00:00:00": 100000.0, "2020-02-10 00:00:00": 100000.0, "2020-02-11 00:00:00": 100000.0, "2020-02-12 00:00:00": 100000.0, "2020-02-13 00:00:00": 100000.0, "2020-02-14 00:00:00": 100000.0, "2020-02-17 00:00:00": 100000.0, "2020-02-18 00:00:00": 100000.0, "2020-02-19 00:00:00": 100000.0, "2020-02-20 00:00:00": 100000.0, "2020-02-21 00:00:00": 99870.13885844238, "2020-02-24 00:00:00": 100481.58868818794, "2020-02-25 00:00:00": 101908.30495759426, "2020-02-26 00:00:00": 100073.95546835757, "2020-02-27 00:00:00": 100379.68038323034, "2020-02-28 00:00:00": 94468.99869568988, "2020-03-02 00:00:00": 99156.78072373923, "2020-03-03 00:00:00": 98647.23919895124, "2020-03-04 00:00:00": 99462.505638612, "2020-03-05 00:00:00": 100889.22190801833, "2020-03-06 00:00:00": 101194.9468228911, "2020-03-09 00:00:00": 97933.88106424808, "2020-03-10 00:00:00": 99156.78072373923, "2020-03-11 00:00:00": 99156.78072373923, "2020-03-12 00:00:00": 97195.94127062704, "2020-03-13 00:00:00": 97195.94127062704, "2020-03-16 00:00:00": 97195.94127062704, "2020-03-17 00:00:00": 97195.94127062704, "2020-03-18 00:00:00": 97195.94127062704, "2020-03-19 00:00:00": 97195.94127062704, "2020-03-20 00:00:00": 97195.94127062704, "2020-03-23 00:00:00": 97195.94127062704, "2020-03-24 00:00:00": 97195.94127062704, "2020-03-25 00:00:00": 97195.94127062704, "2020-03-26 00:00:00": 97195.94127062704, "2020-03-27 00:00:00": 97195.94127062704, "2020-03-30 00:00:00": 97195.94127062704, "2020-03-31 00:00:00": 97195.94127062704, "2020-04-01 00:00:00": 97195.94127062704, "2020-04-02 00:00:00": 97195.94127062704, "2020-04-03 00:00:00": 97195.94127062704, "2020-04-07 00:00:00": 97195.94127062704, "2020-04-08 00:00:00": 97195.94127062704, "2020-04-09 00:00:00": 97195.94127062704, "2020-04-10 00:00:00": 97195.94127062704, "2020-04-13 00:00:00": 97069.72151174535, "2020-04-14 00:00:00": 97924.02049094663, "2020-04-15 00:00:00": 97603.65837374615, "2020-04-16 00:00:00": 97710.4457461463, "2020-04-17 00:00:00": 96856.14676694502, "2020-04-20 00:00:00": 98671.53209774774, "2020-04-21 00:00:00": 97603.65837374615, "2020-04-22 00:00:00": 99098.68158734837, "2020-04-23 00:00:00": 100273.34268375015, "2020-04-24 00:00:00": 100486.91742855046, "2020-04-27 00:00:00": 100700.49217335078, "2020-04-28 00:00:00": 100914.06691815109, "2020-04-29 00:00:00": 102302.30275935317, "2020-04-30 00:00:00": 102302.30275935317, "2020-05-06 00:00:00": 105078.77444175733, "2020-05-07 00:00:00": 104117.68809015589, "2020-05-08 00:00:00": 105505.92393135799, "2020-05-11 00:00:00": 108495.97035856245, "2020-05-12 00:00:00": 108495.97035856245, "2020-05-13 00:00:00": 106894.15977256004, "2020-05-14 00:00:00": 107214.52188976052, "2020-05-15 00:00:00": 105933.07342095861, "2020-05-18 00:00:00": 102836.23962135399, "2020-05-19 00:00:00": 103904.11334535558, "2020-05-20 00:00:00": 103797.32597295543, "2020-05-21 00:00:00": 106360.22291055926, "2020-05-22 00:00:00": 108068.8208689618, "2020-05-25 00:00:00": 110097.78094456486, "2020-05-26 00:00:00": 110311.35568936517, "2020-05-27 00:00:00": 111806.37890296741, "2020-05-28 00:00:00": 111592.80415816708, "2020-05-29 00:00:00": 110418.14306176532, "2020-06-01 00:00:00": 114903.21270257204, "2020-06-02 00:00:00": 113408.1894889698, "2020-06-03 00:00:00": 112340.3157649682, "2020-06-04 00:00:00": 113835.33897857045, "2020-06-05 00:00:00": 113728.5516061703, "2020-06-08 00:00:00": 113728.5516061703, "2020-06-09 00:00:00": 117466.10964017588, "2020-06-10 00:00:00": 119388.28234337876, "2020-06-11 00:00:00": 119067.92022617828, "2020-06-12 00:00:00": 121737.60453618228, "2020-06-15 00:00:00": 124620.8635909866, "2020-06-16 00:00:00": 123125.84037738435, "2020-06-17 00:00:00": 123019.05300498419, "2020-06-18 00:00:00": 125154.8004529874, "2020-06-19 00:00:00": 126649.82366658963, "2020-06-22 00:00:00": 128785.57111459284, "2020-06-23 00:00:00": 134338.51447940114, "2020-06-24 00:00:00": 135513.1755758029, "2020-06-29 00:00:00": 133057.06601059923, "2020-06-30 00:00:00": 140638.96945101058, "2020-07-01 00:00:00": 143201.8663886144, "2020-07-02 00:00:00": 148220.87289142195, "2020-07-03 00:00:00": 145444.40120901776, "2020-07-06 00:00:00": 158579.24801423744, "2020-07-07 00:00:00": 172034.45693665757, "2020-07-08 00:00:00": 169257.98525425344, "2020-07-09 00:00:00": 168083.32415785166, "2020-07-10 00:00:00": 166267.93882704896, "2020-07-13 00:00:00": 176946.67606706495, "2020-07-14 00:00:00": 174276.99175706095, "2020-07-15 00:00:00": 178228.12453586687, "2020-07-16 00:00:00": 173849.8422674603, "2020-07-17 00:00:00": 164132.19137904575, "2020-07-20 00:00:00": 169791.92211625422, "2020-07-21 00:00:00": 174063.41701226064, "2020-07-22 00:00:00": 169151.19788185327, "2020-07-23 00:00:00": 168083.32415785166, "2020-07-24 00:00:00": 162103.2313034427, "2020-07-27 00:00:00": 174490.56650186126, "2020-07-28 00:00:00": 174063.41701226064, "2020-07-29 00:00:00": 175238.0781086624, "2020-07-30 00:00:00": 172888.75591585887, "2020-07-31 00:00:00": 170112.2842334547, "2020-08-03 00:00:00": 175238.0781086624, "2020-08-04 00:00:00": 170859.79584025583, "2020-08-05 00:00:00": 170432.6463506552, "2020-08-06 00:00:00": 183460.7057834747, "2020-08-07 00:00:00": 181752.10782507213, "2020-08-10 00:00:00": 171180.1579574563, "2020-08-11 00:00:00": 168617.26101985245, "2020-08-12 00:00:00": 177480.61292906574, "2020-08-13 00:00:00": 187732.2006794811, "2020-08-14 00:00:00": 185596.45323147788, "2020-08-17 00:00:00": 185916.81534867838, "2020-08-18 00:00:00": 196168.40309909373, "2020-08-19 00:00:00": 181004.596218271, "2020-08-20 00:00:00": 181431.74570787165, "2020-08-21 00:00:00": 184955.72899707695, "2020-08-24 00:00:00": 187198.2638174803, "2020-08-25 00:00:00": 185703.24060387805, "2020-08-26 00:00:00": 185062.5163694771, "2020-08-27 00:00:00": 195100.52937509213, "2020-08-28 00:00:00": 202789.22018790361, "2020-08-31 00:00:00": 204070.66865670556, "2020-09-01 00:00:00": 206206.41610470874, "2020-09-02 00:00:00": 197663.42631269598, "2020-09-03 00:00:00": 186130.3900934787, "2020-09-04 00:00:00": 183567.49315587486, "2020-09-07 00:00:00": 180470.65935627022, "2020-09-08 00:00:00": 182689.01899990664, "2020-09-09 00:00:00": 182689.01899990664, "2020-09-10 00:00:00": 182689.01899990664, "2020-09-11 00:00:00": 182689.01899990664, "2020-09-14 00:00:00": 182689.01899990664, "2020-09-15 00:00:00": 182689.01899990664, "2020-09-16 00:00:00": 182689.01899990664, "2020-09-17 00:00:00": 182689.01899990664, "2020-09-18 00:00:00": 182689.01899990664, "2020-09-21 00:00:00": 182689.01899990664, "2020-09-22 00:00:00": 182689.01899990664, "2020-09-23 00:00:00": 182689.01899990664, "2020-09-24 00:00:00": 182689.01899990664, "2020-09-25 00:00:00": 182689.01899990664, "2020-09-28 00:00:00": 182689.01899990664, "2020-09-29 00:00:00": 182689.01899990664, "2020-09-30 00:00:00": 182689.01899990664, "2020-10-09 00:00:00": 182689.01899990664, "2020-10-12 00:00:00": 182689.01899990664, "2020-10-13 00:00:00": 182689.01899990664, "2020-10-14 00:00:00": 182451.77695433295, "2020-10-15 00:00:00": 166535.99514029236, "2020-10-16 00:00:00": 156845.6499861004, "2020-10-19 00:00:00": 156845.6499861004, "2020-10-20 00:00:00": 156845.6499861004, "2020-10-21 00:00:00": 156845.6499861004, "2020-10-22 00:00:00": 156845.6499861004, "2020-10-23 00:00:00": 156845.6499861004, "2020-10-26 00:00:00": 156845.6499861004, "2020-10-27 00:00:00": 156845.6499861004, "2020-10-28 00:00:00": 156845.6499861004, "2020-10-29 00:00:00": 156845.6499861004, "2020-10-30 00:00:00": 156641.96843454498, "2020-11-02 00:00:00": 153737.6056081937, "2020-11-03 00:00:00": 161676.19733355384, "2020-11-04 00:00:00": 164870.99644254023, "2020-11-05 00:00:00": 164483.74806569336, "2020-11-06 00:00:00": 162741.13036988262, "2020-11-09 00:00:00": 165258.24481938707, "2020-11-10 00:00:00": 166613.614138351, "2020-11-11 00:00:00": 161773.00942776556, "2020-11-12 00:00:00": 164096.49968884655, "2020-11-13 00:00:00": 164483.74806569336, "2020-11-16 00:00:00": 166710.4262325627, "2020-11-17 00:00:00": 166710.4262325627, "2020-11-18 00:00:00": 168646.66811679688, "2020-11-19 00:00:00": 169808.41324733736, "2020-11-20 00:00:00": 172131.9035084184, "2020-11-23 00:00:00": 175423.5147116165, "2020-11-24 00:00:00": 172228.71560263008, "2020-11-25 00:00:00": 172228.71560263008, "2020-11-26 00:00:00": 172228.71560263008, "2020-11-27 00:00:00": 167581.73508046806, "2020-11-30 00:00:00": 158481.39822456744, "2020-12-01 00:00:00": 152957.67523698756, "2020-12-02 00:00:00": 152957.67523698756, "2020-12-03 00:00:00": 152957.67523698756, "2020-12-04 00:00:00": 152957.67523698756, "2020-12-07 00:00:00": 152957.67523698756, "2020-12-08 00:00:00": 152957.67523698756, "2020-12-09 00:00:00": 152957.67523698756, "2020-12-10 00:00:00": 152957.67523698756, "2020-12-11 00:00:00": 152957.67523698756, "2020-12-14 00:00:00": 152957.67523698756, "2020-12-15 00:00:00": 152957.67523698756, "2020-12-16 00:00:00": 152957.67523698756, "2020-12-17 00:00:00": 152957.67523698756, "2020-12-18 00:00:00": 152957.67523698756, "2020-12-21 00:00:00": 152957.67523698756, "2020-12-22 00:00:00": 152957.67523698756, "2020-12-23 00:00:00": 152957.67523698756, "2020-12-24 00:00:00": 152957.67523698756, "2020-12-25 00:00:00": 152759.0426538248, "2020-12-28 00:00:00": 149718.05308540096, "2020-12-29 00:00:00": 147386.62774960932, "2020-12-30 00:00:00": 151542.64682645525, "2020-12-31 00:00:00": 157219.16068751313}, "monthly_returns": {"2020-01-31 00:00:00": 0.0, "2020-02-29 00:00:00": -0.05531001304310135, "2020-03-31 00:00:00": 0.028866004854368743, "2020-04-30 00:00:00": 0.05253677696796255, "2020-05-31 00:00:00": 0.07933194154488543, "2020-06-30 00:00:00": 0.27369439071566704, "2020-07-31 00:00:00": 0.20956719817767677, "2020-08-31 00:00:00": 0.19962335216572558, "2020-09-30 00:00:00": -0.10477571224489812, "2020-10-31 00:00:00": -0.14257589595669662, "2020-11-30 00:00:00": 0.011742892459826626, "2020-12-31 00:00:00": -0.007964578500662345}}, "risk": {"drawdowns": {"2020-01-02 00:00:00": 0.0, "2020-01-03 00:00:00": 0.0, "2020-01-06 00:00:00": 0.0, "2020-01-07 00:00:00": 0.0, "2020-01-08 00:00:00": 0.0, "2020-01-09 00:00:00": 0.0, "2020-01-10 00:00:00": 0.0, "2020-01-13 00:00:00": 0.0, "2020-01-14 00:00:00": 0.0, "2020-01-15 00:00:00": 0.0, "2020-01-16 00:00:00": 0.0, "2020-01-17 00:00:00": 0.0, "2020-01-20 00:00:00": 0.0, "2020-01-21 00:00:00": 0.0, "2020-01-22 00:00:00": 0.0, "2020-01-23 00:00:00": 0.0, "2020-02-03 00:00:00": 0.0, "2020-02-04 00:00:00": 0.0, "2020-02-05 00:00:00": 0.0, "2020-02-06 00:00:00": 0.0, "2020-02-07 00:00:00": 0.0, "2020-02-10 00:00:00": 0.0, "2020-02-11 00:00:00": 0.0, "2020-02-12 00:00:00": 0.0, "2020-02-13 00:00:00": 0.0, "2020-02-14 00:00:00": 0.0, "2020-02-17 00:00:00": 0.0, "2020-02-18 00:00:00": 0.0, "2020-02-19 00:00:00": 0.0, "2020-02-20 00:00:00": 0.0, "2020-02-21 00:00:00": -0.0012986114155763007, "2020-02-24 00:00:00": 0.0, "2020-02-25 00:00:00": 0.0, "2020-02-26 00:00:00": -0.017999999999999905, "2020-02-27 00:00:00": -0.015000000000000013, "2020-02-28 00:00:00": -0.07299999999999995, "2020-03-02 00:00:00": -0.026999999999999802, "2020-03-03 00:00:00": -0.03200000000000003, "2020-03-04 00:00:00": -0.02399999999999991, "2020-03-05 00:00:00": -0.010000000000000009, "2020-03-06 00:00:00": -0.007000000000000117, "2020-03-09 00:00:00": -0.039000000000000035, "2020-03-10 00:00:00": -0.027000000000000024, "2020-03-11 00:00:00": -0.027000000000000024, "2020-03-12 00:00:00": -0.04624121350000032, "2020-03-13 00:00:00": -0.04624121350000032, "2020-03-16 00:00:00": -0.04624121350000032, "2020-03-17 00:00:00": -0.04624121350000032, "2020-03-18 00:00:00": -0.04624121350000032, "2020-03-19 00:00:00": -0.04624121350000032, "2020-03-20 00:00:00": -0.04624121350000032, "2020-03-23 00:00:00": -0.04624121350000032, "2020-03-24 00:00:00": -0.04624121350000032, "2020-03-25 00:00:00": -0.04624121350000032, "2020-03-26 00:00:00": -0.04624121350000032, "2020-03-27 00:00:00": -0.04624121350000032, "2020-03-30 00:00:00": -0.04624121350000032, "2020-03-31 00:00:00": -0.04624121350000032, "2020-04-01 00:00:00": -0.04624121350000032, "2020-04-02 00:00:00": -0.04624121350000032, "2020-04-03 00:00:00": -0.04624121350000032, "2020-04-07 00:00:00": -0.04624121350000032, "2020-04-08 00:00:00": -0.04624121350000032, "2020-04-09 00:00:00": -0.04624121350000032, "2020-04-10 00:00:00": -0.04624121350000032, "2020-04-13 00:00:00": -0.047479775547854874, "2020-04-14 00:00:00": -0.03909675927104839, "2020-04-15 00:00:00": -0.042240390374850834, "2020-04-16 00:00:00": -0.04119251334024998, "2020-04-17 00:00:00": -0.04957552961705658, "2020-04-20 00:00:00": -0.03176162002884264, "2020-04-21 00:00:00": -0.04224039037485072, "2020-04-22 00:00:00": -0.02757011189043934, "2020-04-23 00:00:00": -0.016043464509830296, "2020-04-24 00:00:00": -0.013947710440628702, "2020-04-27 00:00:00": -0.011851956371426997, "2020-04-28 00:00:00": -0.009756202302225514, "2020-04-29 00:00:00": 0.0, "2020-04-30 00:00:00": 0.0, "2020-05-06 00:00:00": 0.0, "2020-05-07 00:00:00": -0.009146341463414642, "2020-05-08 00:00:00": 0.0, "2020-05-11 00:00:00": 0.0, "2020-05-12 00:00:00": 0.0, "2020-05-13 00:00:00": -0.014763779527559251, "2020-05-14 00:00:00": -0.011811023622047445, "2020-05-15 00:00:00": -0.02362204724409467, "2020-05-18 00:00:00": -0.05216535433070857, "2020-05-19 00:00:00": -0.04232283464566933, "2020-05-20 00:00:00": -0.04330708661417315, "2020-05-21 00:00:00": -0.019685039370078816, "2020-05-22 00:00:00": -0.003937007874015852, "2020-05-25 00:00:00": 0.0, "2020-05-26 00:00:00": 0.0, "2020-05-27 00:00:00": 0.0, "2020-05-28 00:00:00": -0.0019102196752627254, "2020-05-29 00:00:00": -0.012416427889207382, "2020-06-01 00:00:00": 0.0, "2020-06-02 00:00:00": -0.013011152416356975, "2020-06-03 00:00:00": -0.02230483271375483, "2020-06-04 00:00:00": -0.009293680297397855, "2020-06-05 00:00:00": -0.010223048327137607, "2020-06-08 00:00:00": -0.010223048327137607, "2020-06-09 00:00:00": 0.0, "2020-06-10 00:00:00": 0.0, "2020-06-11 00:00:00": -0.0026833631484793896, "2020-06-12 00:00:00": 0.0, "2020-06-15 00:00:00": 0.0, "2020-06-16 00:00:00": -0.011996572407883543, "2020-06-17 00:00:00": -0.01285347043701801, "2020-06-18 00:00:00": 0.0, "2020-06-19 00:00:00": 0.0, "2020-06-22 00:00:00": 0.0, "2020-06-23 00:00:00": 0.0, "2020-06-24 00:00:00": 0.0, "2020-06-29 00:00:00": -0.01812450748620953, "2020-06-30 00:00:00": 0.0, "2020-07-01 00:00:00": 0.0, "2020-07-02 00:00:00": 0.0, "2020-07-03 00:00:00": -0.018731988472622585, "2020-07-06 00:00:00": 0.0, "2020-07-07 00:00:00": 0.0, "2020-07-08 00:00:00": -0.016139044072004727, "2020-07-09 00:00:00": -0.022967101179391713, "2020-07-10 00:00:00": -0.03351955307262544, "2020-07-13 00:00:00": 0.0, "2020-07-14 00:00:00": -0.015087507543753875, "2020-07-15 00:00:00": 0.0, "2020-07-16 00:00:00": -0.024565608148591878, "2020-07-17 00:00:00": -0.07908927501497887, "2020-07-20 00:00:00": -0.04733373277411601, "2020-07-21 00:00:00": -0.0233672857998799, "2020-07-22 00:00:00": -0.0509286998202515, "2020-07-23 00:00:00": -0.0569203115638105, "2020-07-24 00:00:00": -0.09047333732774099, "2020-07-27 00:00:00": -0.020970641102456167, "2020-07-28 00:00:00": -0.0233672857998799, "2020-07-29 00:00:00": -0.01677651288196491, "2020-07-30 00:00:00": -0.029958058717794778, "2020-07-31 00:00:00": -0.045536249251048266, "2020-08-03 00:00:00": -0.01677651288196491, "2020-08-04 00:00:00": -0.04134212103055679, "2020-08-05 00:00:00": -0.0437387657279803, "2020-08-06 00:00:00": 0.0, "2020-08-07 00:00:00": -0.00931315483119921, "2020-08-10 00:00:00": -0.06693830034924342, "2020-08-11 00:00:00": -0.08090803259604218, "2020-08-12 00:00:00": -0.03259604190919685, "2020-08-13 00:00:00": 0.0, "2020-08-14 00:00:00": -0.011376564277588375, "2020-08-17 00:00:00": -0.009670079635949902, "2020-08-18 00:00:00": 0.0, "2020-08-19 00:00:00": -0.07729994556341879, "2020-08-20 00:00:00": -0.07512248230811114, "2020-08-21 00:00:00": -0.05715841045182368, "2020-08-24 00:00:00": -0.04572672836145886, "2020-08-25 00:00:00": -0.05334784975503548, "2020-08-26 00:00:00": -0.05661404463799691, "2020-08-27 00:00:00": -0.00544365813826897, "2020-08-28 00:00:00": 0.0, "2020-08-31 00:00:00": 0.0, "2020-09-01 00:00:00": 0.0, "2020-09-02 00:00:00": -0.04142931123770055, "2020-09-03 00:00:00": -0.09735888140859672, "2020-09-04 00:00:00": -0.10978767477990681, "2020-09-07 00:00:00": -0.12480580010357345, "2020-09-08 00:00:00": -0.11404784365613707, "2020-09-09 00:00:00": -0.11404784365613707, "2020-09-10 00:00:00": -0.11404784365613707, "2020-09-11 00:00:00": -0.11404784365613707, "2020-09-14 00:00:00": -0.11404784365613707, "2020-09-15 00:00:00": -0.11404784365613707, "2020-09-16 00:00:00": -0.11404784365613707, "2020-09-17 00:00:00": -0.11404784365613707, "2020-09-18 00:00:00": -0.11404784365613707, "2020-09-21 00:00:00": -0.11404784365613707, "2020-09-22 00:00:00": -0.11404784365613707, "2020-09-23 00:00:00": -0.11404784365613707, "2020-09-24 00:00:00": -0.11404784365613707, "2020-09-25 00:00:00": -0.11404784365613707, "2020-09-28 00:00:00": -0.11404784365613707, "2020-09-29 00:00:00": -0.11404784365613707, "2020-09-30 00:00:00": -0.11404784365613707, "2020-10-09 00:00:00": -0.11404784365613707, "2020-10-12 00:00:00": -0.11404784365613707, "2020-10-13 00:00:00": -0.11404784365613707, "2020-10-14 00:00:00": -0.11519835124001943, "2020-10-15 00:00:00": -0.19238208836466253, "2020-10-16 00:00:00": -0.2393755104765687, "2020-10-19 00:00:00": -0.2393755104765687, "2020-10-20 00:00:00": -0.2393755104765687, "2020-10-21 00:00:00": -0.2393755104765687, "2020-10-22 00:00:00": -0.2393755104765687, "2020-10-23 00:00:00": -0.2393755104765687, "2020-10-26 00:00:00": -0.2393755104765687, "2020-10-27 00:00:00": -0.2393755104765687, "2020-10-28 00:00:00": -0.2393755104765687, "2020-10-29 00:00:00": -0.2393755104765687, "2020-10-30 00:00:00": -0.24036326612163073, "2020-11-02 00:00:00": -0.2544480016076327, "2020-11-03 00:00:00": -0.2159497246125609, "2020-11-04 00:00:00": -0.20045651557795885, "2020-11-05 00:00:00": -0.2023344803094258, "2020-11-06 00:00:00": -0.21078532160102703, "2020-11-09 00:00:00": -0.19857855084649212, "2020-11-10 00:00:00": -0.19200567428635784, "2020-11-11 00:00:00": -0.21548023342969425, "2020-11-12 00:00:00": -0.20421244504089286, "2020-11-13 00:00:00": -0.20233448030942602, "2020-11-16 00:00:00": -0.1915361831034912, "2020-11-17 00:00:00": -0.1915361831034912, "2020-11-18 00:00:00": -0.18214635944615631, "2020-11-19 00:00:00": -0.17651246525175568, "2020-11-20 00:00:00": -0.16524467686295385, "2020-11-23 00:00:00": -0.14928197664548526, "2020-11-24 00:00:00": -0.16477518568008742, "2020-11-25 00:00:00": -0.16477518568008742, "2020-11-26 00:00:00": -0.16477518568008742, "2020-11-27 00:00:00": -0.1873107624576904, "2020-11-30 00:00:00": -0.23144293364716295, "2020-12-01 00:00:00": -0.25823028145100146, "2020-12-02 00:00:00": -0.25823028145100146, "2020-12-03 00:00:00": -0.25823028145100146, "2020-12-04 00:00:00": -0.25823028145100146, "2020-12-07 00:00:00": -0.25823028145100146, "2020-12-08 00:00:00": -0.25823028145100146, "2020-12-09 00:00:00": -0.25823028145100146, "2020-12-10 00:00:00": -0.25823028145100146, "2020-12-11 00:00:00": -0.25823028145100146, "2020-12-14 00:00:00": -0.25823028145100146, "2020-12-15 00:00:00": -0.25823028145100146, "2020-12-16 00:00:00": -0.25823028145100146, "2020-12-17 00:00:00": -0.25823028145100146, "2020-12-18 00:00:00": -0.25823028145100146, "2020-12-21 00:00:00": -0.25823028145100146, "2020-12-22 00:00:00": -0.25823028145100146, "2020-12-23 00:00:00": -0.25823028145100146, "2020-12-24 00:00:00": -0.25823028145100146, "2020-12-25 00:00:00": -0.25919355207523787, "2020-12-28 00:00:00": -0.2739408602621941, "2020-12-29 00:00:00": -0.2852471298721938, "2020-12-30 00:00:00": -0.26509247535002056, "2020-12-31 00:00:00": -0.23756416673436898}, "rolling_volatility": {"2020-01-02 00:00:00": NaN, "2020-01-03 00:00:00": NaN, "2020-01-06 00:00:00": NaN, "2020-01-07 00:00:00": NaN, "2020-01-08 00:00:00": NaN, "2020-01-09 00:00:00": NaN, "2020-01-10 00:00:00": NaN, "2020-01-13 00:00:00": NaN, "2020-01-14 00:00:00": NaN, "2020-01-15 00:00:00": NaN, "2020-01-16 00:00:00": NaN, "2020-01-17 00:00:00": NaN, "2020-01-20 00:00:00": NaN, "2020-01-21 00:00:00": NaN, "2020-01-22 00:00:00": NaN, "2020-01-23 00:00:00": NaN, "2020-02-03 00:00:00": NaN, "2020-02-04 00:00:00": NaN, "2020-02-05 00:00:00": NaN, "2020-02-06 00:00:00": NaN, "2020-02-07 00:00:00": NaN, "2020-02-10 00:00:00": NaN, "2020-02-11 00:00:00": NaN, "2020-02-12 00:00:00": NaN, "2020-02-13 00:00:00": NaN, "2020-02-14 00:00:00": NaN, "2020-02-17 00:00:00": NaN, "2020-02-18 00:00:00": NaN, "2020-02-19 00:00:00": NaN, "2020-02-20 00:00:00": 0.0, "2020-02-21 00:00:00": 0.0037637334540033046, "2020-02-24 00:00:00": 0.01826582619504039, "2020-02-25 00:00:00": 0.044580798529090845, "2020-02-26 00:00:00": 0.07005289153847856, "2020-02-27 00:00:00": 0.07059741269716865, "2020-02-28 00:00:00": 0.18506182599381973, "2020-03-02 00:00:00": 0.23771287523118703, "2020-03-03 00:00:00": 0.23814658807648684, "2020-03-04 00:00:00": 0.2394513457530229, "2020-03-05 00:00:00": 0.243068735648904, "2020-03-06 00:00:00": 0.24318300921276603, "2020-03-09 00:00:00": 0.2610502314744149, "2020-03-10 00:00:00": 0.263778897333316, "2020-03-11 00:00:00": 0.263778897333316, "2020-03-12 00:00:00": 0.26983981675154095, "2020-03-13 00:00:00": 0.26983981675154095, "2020-03-16 00:00:00": 0.26983981675154095, "2020-03-17 00:00:00": 0.26983981675154095, "2020-03-18 00:00:00": 0.26983981675154095, "2020-03-19 00:00:00": 0.26983981675154095, "2020-03-20 00:00:00": 0.26983981675154095, "2020-03-23 00:00:00": 0.26983981675154095, "2020-03-24 00:00:00": 0.26983981675154095, "2020-03-25 00:00:00": 0.26983981675154095, "2020-03-26 00:00:00": 0.26983981675154095, "2020-03-27 00:00:00": 0.26983981675154095, "2020-03-30 00:00:00": 0.26983981675154095, "2020-03-31 00:00:00": 0.26983981675154095, "2020-04-01 00:00:00": 0.26983981675154095, "2020-04-02 00:00:00": 0.26983981675154095, "2020-04-03 00:00:00": 0.2698454876127506, "2020-04-07 00:00:00": 0.26907022983311674, "2020-04-08 00:00:00": 0.2652353723406611, "2020-04-09 00:00:00": 0.2605593575905768, "2020-04-10 00:00:00": 0.2603127782526592, "2020-04-13 00:00:00": 0.19398716637348842, "2020-04-14 00:00:00": 0.13083292712051078, "2020-04-15 00:00:00": 0.13035427229489097, "2020-04-16 00:00:00": 0.12788342665941732, "2020-04-17 00:00:00": 0.12186544371205323, "2020-04-20 00:00:00": 0.13459494527164104, "2020-04-21 00:00:00": 0.10137415378304146, "2020-04-22 00:00:00": 0.1047033559610457, "2020-04-23 00:00:00": 0.11019402647718904, "2020-04-24 00:00:00": 0.09216308008468048, "2020-04-27 00:00:00": 0.09214314111470732, "2020-04-28 00:00:00": 0.09210862980983729, "2020-04-29 00:00:00": 0.09884546260352944, "2020-04-30 00:00:00": 0.09884546260352944, "2020-05-06 00:00:00": 0.123057404189204, "2020-05-07 00:00:00": 0.12753090108207726, "2020-05-08 00:00:00": 0.13121861250674163, "2020-05-11 00:00:00": 0.15033303780279314, "2020-05-12 00:00:00": 0.15033303780279314, "2020-05-13 00:00:00": 0.15932472752637414, "2020-05-14 00:00:00": 0.15903431136077253, "2020-05-15 00:00:00": 0.16487043454347663, "2020-05-18 00:00:00": 0.18933360649188802, "2020-05-19 00:00:00": 0.19079037056786696, "2020-05-20 00:00:00": 0.1909210902454528, "2020-05-21 00:00:00": 0.20149722611519247, "2020-05-22 00:00:00": 0.2047129356423297, "2020-05-25 00:00:00": 0.20901381483024414, "2020-05-26 00:00:00": 0.20874675340857962, "2020-05-27 00:00:00": 0.2100061033302462, "2020-05-28 00:00:00": 0.2101669330612712, "2020-05-29 00:00:00": 0.2143474739794662, "2020-06-01 00:00:00": 0.2377067249242425, "2020-06-02 00:00:00": 0.24345619854078868, "2020-06-03 00:00:00": 0.2437961162221333, "2020-06-04 00:00:00": 0.24164655395785972, "2020-06-05 00:00:00": 0.23772871976635299, "2020-06-08 00:00:00": 0.23621413490361623, "2020-06-09 00:00:00": 0.24922831072660204, "2020-06-10 00:00:00": 0.2510097192283553, "2020-06-11 00:00:00": 0.2520199231337588, "2020-06-12 00:00:00": 0.25633091247006917, "2020-06-15 00:00:00": 0.26039014865396, "2020-06-16 00:00:00": 0.265358005017116, "2020-06-17 00:00:00": 0.2586006151470849, "2020-06-18 00:00:00": 0.2570551479451215, "2020-06-19 00:00:00": 0.256755470323282, "2020-06-22 00:00:00": 0.2502405348625969, "2020-06-23 00:00:00": 0.2717594125165704, "2020-06-24 00:00:00": 0.2636022004323124, "2020-06-29 00:00:00": 0.2740366739958454, "2020-06-30 00:00:00": 0.3030965640036171, "2020-07-01 00:00:00": 0.2805409626906875, "2020-07-02 00:00:00": 0.2888657710161276, "2020-07-03 00:00:00": 0.30017662991000815, "2020-07-06 00:00:00": 0.37595256723422, "2020-07-07 00:00:00": 0.42889383878282095, "2020-07-08 00:00:00": 0.43872617160104005, "2020-07-09 00:00:00": 0.44173172596265153, "2020-07-10 00:00:00": 0.44778265175806564, "2020-07-13 00:00:00": 0.46831955370703354, "2020-07-14 00:00:00": 0.47073573793348766, "2020-07-15 00:00:00": 0.46533611045207507, "2020-07-16 00:00:00": 0.47255944981184334, "2020-07-17 00:00:00": 0.5108686721152189, "2020-07-20 00:00:00": 0.5145777672411506, "2020-07-21 00:00:00": 0.5135905210082437, "2020-07-22 00:00:00": 0.5269871164421905, "2020-07-23 00:00:00": 0.526931343251003, "2020-07-24 00:00:00": 0.5448524800179074, "2020-07-27 00:00:00": 0.5752208569582888, "2020-07-28 00:00:00": 0.5763618665494786, "2020-07-29 00:00:00": 0.5756244340262386, "2020-07-30 00:00:00": 0.5761504519212951, "2020-07-31 00:00:00": 0.580762914998684, "2020-08-03 00:00:00": 0.5830588438286731, "2020-08-04 00:00:00": 0.592788012177432, "2020-08-05 00:00:00": 0.5936912916995307, "2020-08-06 00:00:00": 0.6172126976480334, "2020-08-07 00:00:00": 0.620037803256706, "2020-08-10 00:00:00": 0.6464301682500074, "2020-08-11 00:00:00": 0.6337443988603386, "2020-08-12 00:00:00": 0.6467957629441661, "2020-08-13 00:00:00": 0.6583081197627854, "2020-08-14 00:00:00": 0.6559837218738304, "2020-08-17 00:00:00": 0.6090993043263997, "2020-08-18 00:00:00": 0.5811307149876783, "2020-08-19 00:00:00": 0.6258023970352553, "2020-08-20 00:00:00": 0.6251008325085845, "2020-08-21 00:00:00": 0.6253163183600187, "2020-08-24 00:00:00": 0.599635243990427, "2020-08-25 00:00:00": 0.5981700426408432, "2020-08-26 00:00:00": 0.5954148190403902, "2020-08-27 00:00:00": 0.6086022111906768, "2020-08-28 00:00:00": 0.5886940927206129, "2020-08-31 00:00:00": 0.5832079471339688, "2020-09-01 00:00:00": 0.5807379014942132, "2020-09-02 00:00:00": 0.5887681312102242, "2020-09-03 00:00:00": 0.6167697907293315, "2020-09-04 00:00:00": 0.6077411084220534, "2020-09-07 00:00:00": 0.5713459877342489, "2020-09-08 00:00:00": 0.5720018186524041, "2020-09-09 00:00:00": 0.5718740540807727, "2020-09-10 00:00:00": 0.5700494844538566, "2020-09-11 00:00:00": 0.5674093293024784, "2020-09-14 00:00:00": 0.561578735428882, "2020-09-15 00:00:00": 0.5557875932367925, "2020-09-16 00:00:00": 0.5556266110327889, "2020-09-17 00:00:00": 0.5100177012231114, "2020-09-18 00:00:00": 0.5091961430980116, "2020-09-21 00:00:00": 0.47772472501521873, "2020-09-22 00:00:00": 0.4748986820402801, "2020-09-23 00:00:00": 0.4511755647316034, "2020-09-24 00:00:00": 0.4182866427946834, "2020-09-25 00:00:00": 0.417029707004388, "2020-09-28 00:00:00": 0.41699085389748763, "2020-09-29 00:00:00": 0.38254952971922895, "2020-09-30 00:00:00": 0.30901425715559067, "2020-10-09 00:00:00": 0.30896604029945746, "2020-10-12 00:00:00": 0.3036647092388032, "2020-10-13 00:00:00": 0.301401803513797, "2020-10-14 00:00:00": 0.3006070638750122, "2020-10-15 00:00:00": 0.39210806198587533, "2020-10-16 00:00:00": 0.38428644687677405, "2020-10-19 00:00:00": 0.3591200696291974, "2020-10-20 00:00:00": 0.3573522024273672, "2020-10-21 00:00:00": 0.3538005322364369, "2020-10-22 00:00:00": 0.340762643075721, "2020-10-23 00:00:00": 0.30512622745251383, "2020-10-26 00:00:00": 0.3044934681084221, "2020-10-27 00:00:00": 0.30271992462144504, "2020-10-28 00:00:00": 0.29887351274675367, "2020-10-29 00:00:00": 0.29887351274675367, "2020-10-30 00:00:00": 0.29871251340118, "2020-11-02 00:00:00": 0.30087714693819223, "2020-11-03 00:00:00": 0.34337638517617486, "2020-11-04 00:00:00": 0.3500041527970008, "2020-11-05 00:00:00": 0.349885358498804, "2020-11-06 00:00:00": 0.3503771338190038, "2020-11-09 00:00:00": 0.35460158648802986, "2020-11-10 00:00:00": 0.3560160860805102, "2020-11-11 00:00:00": 0.36389081444909493, "2020-11-12 00:00:00": 0.36755081906249226, "2020-11-13 00:00:00": 0.3677987229402437, "2020-11-16 00:00:00": 0.37090991449159494, "2020-11-17 00:00:00": 0.37090991449159494, "2020-11-18 00:00:00": 0.37318530379743736, "2020-11-19 00:00:00": 0.37410114695241653, "2020-11-20 00:00:00": 0.3768785227341829, "2020-11-23 00:00:00": 0.38167333586448077, "2020-11-24 00:00:00": 0.38486858194253665, "2020-11-25 00:00:00": 0.3848979049827976, "2020-11-26 00:00:00": 0.2868546151091812, "2020-11-27 00:00:00": 0.24127550910085543, "2020-11-30 00:00:00": 0.29184656457353814, "2020-12-01 00:00:00": 0.30933436624349675, "2020-12-02 00:00:00": 0.30933436624349675, "2020-12-03 00:00:00": 0.30933436624349675, "2020-12-04 00:00:00": 0.30933436624349675, "2020-12-07 00:00:00": 0.30933436624349675, "2020-12-08 00:00:00": 0.30933436624349675, "2020-12-09 00:00:00": 0.30933436624349675, "2020-12-10 00:00:00": 0.30933436624349675, "2020-12-11 00:00:00": 0.3093336660321987, "2020-12-14 00:00:00": 0.30462509056159726, "2020-12-15 00:00:00": 0.2624167137480753, "2020-12-16 00:00:00": 0.25449655448430947, "2020-12-17 00:00:00": 0.2545893112571998, "2020-12-18 00:00:00": 0.2534361422486562, "2020-12-21 00:00:00": 0.24811406918919002, "2020-12-22 00:00:00": 0.24618437365307594, "2020-12-23 00:00:00": 0.23324864000810996, "2020-12-24 00:00:00": 0.22828369915575275, "2020-12-25 00:00:00": 0.2278892786079863, "2020-12-28 00:00:00": 0.22822993587999998, "2020-12-29 00:00:00": 0.23061957956730916, "2020-12-30 00:00:00": 0.24493690809024513, "2020-12-31 00:00:00": 0.27079863124611653}, "underwater": {"2020-01-02 00:00:00": 0.0, "2020-01-03 00:00:00": 0.0, "2020-01-06 00:00:00": 0.0, "2020-01-07 00:00:00": 0.0, "2020-01-08 00:00:00": 0.0, "2020-01-09 00:00:00": 0.0, "2020-01-10 00:00:00": 0.0, "2020-01-13 00:00:00": 0.0, "2020-01-14 00:00:00": 0.0, "2020-01-15 00:00:00": 0.0, "2020-01-16 00:00:00": 0.0, "2020-01-17 00:00:00": 0.0, "2020-01-20 00:00:00": 0.0, "2020-01-21 00:00:00": 0.0, "2020-01-22 00:00:00": 0.0, "2020-01-23 00:00:00": 0.0, "2020-02-03 00:00:00": 0.0, "2020-02-04 00:00:00": 0.0, "2020-02-05 00:00:00": 0.0, "2020-02-06 00:00:00": 0.0, "2020-02-07 00:00:00": 0.0, "2020-02-10 00:00:00": 0.0, "2020-02-11 00:00:00": 0.0, "2020-02-12 00:00:00": 0.0, "2020-02-13 00:00:00": 0.0, "2020-02-14 00:00:00": 0.0, "2020-02-17 00:00:00": 0.0, "2020-02-18 00:00:00": 0.0, "2020-02-19 00:00:00": 0.0, "2020-02-20 00:00:00": 0.0, "2020-02-21 00:00:00": -0.0012986114155763007, "2020-02-24 00:00:00": 0.0, "2020-02-25 00:00:00": 0.0, "2020-02-26 00:00:00": -0.017999999999999905, "2020-02-27 00:00:00": -0.015000000000000013, "2020-02-28 00:00:00": -0.07299999999999995, "2020-03-02 00:00:00": -0.026999999999999802, "2020-03-03 00:00:00": -0.03200000000000003, "2020-03-04 00:00:00": -0.02399999999999991, "2020-03-05 00:00:00": -0.010000000000000009, "2020-03-06 00:00:00": -0.007000000000000117, "2020-03-09 00:00:00": -0.039000000000000035, "2020-03-10 00:00:00": -0.027000000000000024, "2020-03-11 00:00:00": -0.027000000000000024, "2020-03-12 00:00:00": -0.04624121350000032, "2020-03-13 00:00:00": -0.04624121350000032, "2020-03-16 00:00:00": -0.04624121350000032, "2020-03-17 00:00:00": -0.04624121350000032, "2020-03-18 00:00:00": -0.04624121350000032, "2020-03-19 00:00:00": -0.04624121350000032, "2020-03-20 00:00:00": -0.04624121350000032, "2020-03-23 00:00:00": -0.04624121350000032, "2020-03-24 00:00:00": -0.04624121350000032, "2020-03-25 00:00:00": -0.04624121350000032, "2020-03-26 00:00:00": -0.04624121350000032, "2020-03-27 00:00:00": -0.04624121350000032, "2020-03-30 00:00:00": -0.04624121350000032, "2020-03-31 00:00:00": -0.04624121350000032, "2020-04-01 00:00:00": -0.04624121350000032, "2020-04-02 00:00:00": -0.04624121350000032, "2020-04-03 00:00:00": -0.04624121350000032, "2020-04-07 00:00:00": -0.04624121350000032, "2020-04-08 00:00:00": -0.04624121350000032, "2020-04-09 00:00:00": -0.04624121350000032, "2020-04-10 00:00:00": -0.04624121350000032, "2020-04-13 00:00:00": -0.047479775547854874, "2020-04-14 00:00:00": -0.03909675927104839, "2020-04-15 00:00:00": -0.042240390374850834, "2020-04-16 00:00:00": -0.04119251334024998, "2020-04-17 00:00:00": -0.04957552961705658, "2020-04-20 00:00:00": -0.03176162002884264, "2020-04-21 00:00:00": -0.04224039037485072, "2020-04-22 00:00:00": -0.02757011189043934, "2020-04-23 00:00:00": -0.016043464509830296, "2020-04-24 00:00:00": -0.013947710440628702, "2020-04-27 00:00:00": -0.011851956371426997, "2020-04-28 00:00:00": -0.009756202302225514, "2020-04-29 00:00:00": 0.0, "2020-04-30 00:00:00": 0.0, "2020-05-06 00:00:00": 0.0, "2020-05-07 00:00:00": -0.009146341463414642, "2020-05-08 00:00:00": 0.0, "2020-05-11 00:00:00": 0.0, "2020-05-12 00:00:00": 0.0, "2020-05-13 00:00:00": -0.014763779527559251, "2020-05-14 00:00:00": -0.011811023622047445, "2020-05-15 00:00:00": -0.02362204724409467, "2020-05-18 00:00:00": -0.05216535433070857, "2020-05-19 00:00:00": -0.04232283464566933, "2020-05-20 00:00:00": -0.04330708661417315, "2020-05-21 00:00:00": -0.019685039370078816, "2020-05-22 00:00:00": -0.003937007874015852, "2020-05-25 00:00:00": 0.0, "2020-05-26 00:00:00": 0.0, "2020-05-27 00:00:00": 0.0, "2020-05-28 00:00:00": -0.0019102196752627254, "2020-05-29 00:00:00": -0.012416427889207382, "2020-06-01 00:00:00": 0.0, "2020-06-02 00:00:00": -0.013011152416356975, "2020-06-03 00:00:00": -0.02230483271375483, "2020-06-04 00:00:00": -0.009293680297397855, "2020-06-05 00:00:00": -0.010223048327137607, "2020-06-08 00:00:00": -0.010223048327137607, "2020-06-09 00:00:00": 0.0, "2020-06-10 00:00:00": 0.0, "2020-06-11 00:00:00": -0.0026833631484793896, "2020-06-12 00:00:00": 0.0, "2020-06-15 00:00:00": 0.0, "2020-06-16 00:00:00": -0.011996572407883543, "2020-06-17 00:00:00": -0.01285347043701801, "2020-06-18 00:00:00": 0.0, "2020-06-19 00:00:00": 0.0, "2020-06-22 00:00:00": 0.0, "2020-06-23 00:00:00": 0.0, "2020-06-24 00:00:00": 0.0, "2020-06-29 00:00:00": -0.01812450748620953, "2020-06-30 00:00:00": 0.0, "2020-07-01 00:00:00": 0.0, "2020-07-02 00:00:00": 0.0, "2020-07-03 00:00:00": -0.018731988472622585, "2020-07-06 00:00:00": 0.0, "2020-07-07 00:00:00": 0.0, "2020-07-08 00:00:00": -0.016139044072004727, "2020-07-09 00:00:00": -0.022967101179391713, "2020-07-10 00:00:00": -0.03351955307262544, "2020-07-13 00:00:00": 0.0, "2020-07-14 00:00:00": -0.015087507543753875, "2020-07-15 00:00:00": 0.0, "2020-07-16 00:00:00": -0.024565608148591878, "2020-07-17 00:00:00": -0.07908927501497887, "2020-07-20 00:00:00": -0.04733373277411601, "2020-07-21 00:00:00": -0.0233672857998799, "2020-07-22 00:00:00": -0.0509286998202515, "2020-07-23 00:00:00": -0.0569203115638105, "2020-07-24 00:00:00": -0.09047333732774099, "2020-07-27 00:00:00": -0.020970641102456167, "2020-07-28 00:00:00": -0.0233672857998799, "2020-07-29 00:00:00": -0.01677651288196491, "2020-07-30 00:00:00": -0.029958058717794778, "2020-07-31 00:00:00": -0.045536249251048266, "2020-08-03 00:00:00": -0.01677651288196491, "2020-08-04 00:00:00": -0.04134212103055679, "2020-08-05 00:00:00": -0.0437387657279803, "2020-08-06 00:00:00": 0.0, "2020-08-07 00:00:00": -0.00931315483119921, "2020-08-10 00:00:00": -0.06693830034924342, "2020-08-11 00:00:00": -0.08090803259604218, "2020-08-12 00:00:00": -0.03259604190919685, "2020-08-13 00:00:00": 0.0, "2020-08-14 00:00:00": -0.011376564277588375, "2020-08-17 00:00:00": -0.009670079635949902, "2020-08-18 00:00:00": 0.0, "2020-08-19 00:00:00": -0.07729994556341879, "2020-08-20 00:00:00": -0.07512248230811114, "2020-08-21 00:00:00": -0.05715841045182368, "2020-08-24 00:00:00": -0.04572672836145886, "2020-08-25 00:00:00": -0.05334784975503548, "2020-08-26 00:00:00": -0.05661404463799691, "2020-08-27 00:00:00": -0.00544365813826897, "2020-08-28 00:00:00": 0.0, "2020-08-31 00:00:00": 0.0, "2020-09-01 00:00:00": 0.0, "2020-09-02 00:00:00": -0.04142931123770055, "2020-09-03 00:00:00": -0.09735888140859672, "2020-09-04 00:00:00": -0.10978767477990681, "2020-09-07 00:00:00": -0.12480580010357345, "2020-09-08 00:00:00": -0.11404784365613707, "2020-09-09 00:00:00": -0.11404784365613707, "2020-09-10 00:00:00": -0.11404784365613707, "2020-09-11 00:00:00": -0.11404784365613707, "2020-09-14 00:00:00": -0.11404784365613707, "2020-09-15 00:00:00": -0.11404784365613707, "2020-09-16 00:00:00": -0.11404784365613707, "2020-09-17 00:00:00": -0.11404784365613707, "2020-09-18 00:00:00": -0.11404784365613707, "2020-09-21 00:00:00": -0.11404784365613707, "2020-09-22 00:00:00": -0.11404784365613707, "2020-09-23 00:00:00": -0.11404784365613707, "2020-09-24 00:00:00": -0.11404784365613707, "2020-09-25 00:00:00": -0.11404784365613707, "2020-09-28 00:00:00": -0.11404784365613707, "2020-09-29 00:00:00": -0.11404784365613707, "2020-09-30 00:00:00": -0.11404784365613707, "2020-10-09 00:00:00": -0.11404784365613707, "2020-10-12 00:00:00": -0.11404784365613707, "2020-10-13 00:00:00": -0.11404784365613707, "2020-10-14 00:00:00": -0.11519835124001943, "2020-10-15 00:00:00": -0.19238208836466253, "2020-10-16 00:00:00": -0.2393755104765687, "2020-10-19 00:00:00": -0.2393755104765687, "2020-10-20 00:00:00": -0.2393755104765687, "2020-10-21 00:00:00": -0.2393755104765687, "2020-10-22 00:00:00": -0.2393755104765687, "2020-10-23 00:00:00": -0.2393755104765687, "2020-10-26 00:00:00": -0.2393755104765687, "2020-10-27 00:00:00": -0.2393755104765687, "2020-10-28 00:00:00": -0.2393755104765687, "2020-10-29 00:00:00": -0.2393755104765687, "2020-10-30 00:00:00": -0.24036326612163073, "2020-11-02 00:00:00": -0.2544480016076327, "2020-11-03 00:00:00": -0.2159497246125609, "2020-11-04 00:00:00": -0.20045651557795885, "2020-11-05 00:00:00": -0.2023344803094258, "2020-11-06 00:00:00": -0.21078532160102703, "2020-11-09 00:00:00": -0.19857855084649212, "2020-11-10 00:00:00": -0.19200567428635784, "2020-11-11 00:00:00": -0.21548023342969425, "2020-11-12 00:00:00": -0.20421244504089286, "2020-11-13 00:00:00": -0.20233448030942602, "2020-11-16 00:00:00": -0.1915361831034912, "2020-11-17 00:00:00": -0.1915361831034912, "2020-11-18 00:00:00": -0.18214635944615631, "2020-11-19 00:00:00": -0.17651246525175568, "2020-11-20 00:00:00": -0.16524467686295385, "2020-11-23 00:00:00": -0.14928197664548526, "2020-11-24 00:00:00": -0.16477518568008742, "2020-11-25 00:00:00": -0.16477518568008742, "2020-11-26 00:00:00": -0.16477518568008742, "2020-11-27 00:00:00": -0.1873107624576904, "2020-11-30 00:00:00": -0.23144293364716295, "2020-12-01 00:00:00": -0.25823028145100146, "2020-12-02 00:00:00": -0.25823028145100146, "2020-12-03 00:00:00": -0.25823028145100146, "2020-12-04 00:00:00": -0.25823028145100146, "2020-12-07 00:00:00": -0.25823028145100146, "2020-12-08 00:00:00": -0.25823028145100146, "2020-12-09 00:00:00": -0.25823028145100146, "2020-12-10 00:00:00": -0.25823028145100146, "2020-12-11 00:00:00": -0.25823028145100146, "2020-12-14 00:00:00": -0.25823028145100146, "2020-12-15 00:00:00": -0.25823028145100146, "2020-12-16 00:00:00": -0.25823028145100146, "2020-12-17 00:00:00": -0.25823028145100146, "2020-12-18 00:00:00": -0.25823028145100146, "2020-12-21 00:00:00": -0.25823028145100146, "2020-12-22 00:00:00": -0.25823028145100146, "2020-12-23 00:00:00": -0.25823028145100146, "2020-12-24 00:00:00": -0.25823028145100146, "2020-12-25 00:00:00": -0.25919355207523787, "2020-12-28 00:00:00": -0.2739408602621941, "2020-12-29 00:00:00": -0.2852471298721938, "2020-12-30 00:00:00": -0.26509247535002056, "2020-12-31 00:00:00": -0.23756416673436898}}, "trades": {}}, "daily_returns": {"daily_return": {"2020-01-02 00:00:00": 0.0, "2020-01-03 00:00:00": 0.0, "2020-01-06 00:00:00": 0.0, "2020-01-07 00:00:00": 0.0, "2020-01-08 00:00:00": 0.0, "2020-01-09 00:00:00": 0.0, "2020-01-10 00:00:00": 0.0, "2020-01-13 00:00:00": 0.0, "2020-01-14 00:00:00": 0.0, "2020-01-15 00:00:00": 0.0, "2020-01-16 00:00:00": 0.0, "2020-01-17 00:00:00": 0.0, "2020-01-20 00:00:00": 0.0, "2020-01-21 00:00:00": 0.0, "2020-01-22 00:00:00": 0.0, "2020-01-23 00:00:00": 0.0, "2020-02-03 00:00:00": 0.0, "2020-02-04 00:00:00": 0.0, "2020-02-05 00:00:00": 0.0, "2020-02-06 00:00:00": 0.0, "2020-02-07 00:00:00": 0.0, "2020-02-10 00:00:00": 0.0, "2020-02-11 00:00:00": 0.0, "2020-02-12 00:00:00": 0.0, "2020-02-13 00:00:00": 0.0, "2020-02-14 00:00:00": 0.0, "2020-02-17 00:00:00": 0.0, "2020-02-18 00:00:00": 0.0, "2020-02-19 00:00:00": 0.0, "2020-02-20 00:00:00": 0.0, "2020-02-21 00:00:00": -0.0012986114155761606, "2020-02-24 00:00:00": 0.006122448979591756, "2020-02-25 00:00:00": 0.014198782961460404, "2020-02-26 00:00:00": -0.017999999999999905, "2020-02-27 00:00:00": 0.0030549898167004977, "2020-02-28 00:00:00": -0.05888324873096442, "2020-03-02 00:00:00": 0.04962243797195265, "2020-03-03 00:00:00": -0.005138746145940542, "2020-03-04 00:00:00": 0.008264462809917394, "2020-03-05 00:00:00": 0.014344262295082072, "2020-03-06 00:00:00": 0.003030303030302918, "2020-03-09 00:00:00": -0.03222557905337362, "2020-03-10 00:00:00": 0.012486992715921049, "2020-03-11 00:00:00": 0.0, "2020-03-12 00:00:00": -0.01977514234326833, "2020-03-13 00:00:00": 0.0, "2020-03-16 00:00:00": 0.0, "2020-03-17 00:00:00": 0.0, "2020-03-18 00:00:00": 0.0, "2020-03-19 00:00:00": 0.0, "2020-03-20 00:00:00": 0.0, "2020-03-23 00:00:00": 0.0, "2020-03-24 00:00:00": 0.0, "2020-03-25 00:00:00": 0.0, "2020-03-26 00:00:00": 0.0, "2020-03-27 00:00:00": 0.0, "2020-03-30 00:00:00": 0.0, "2020-03-31 00:00:00": 0.0, "2020-04-01 00:00:00": 0.0, "2020-04-02 00:00:00": 0.0, "2020-04-03 00:00:00": 0.0, "2020-04-07 00:00:00": 0.0, "2020-04-08 00:00:00": 0.0, "2020-04-09 00:00:00": 0.0, "2020-04-10 00:00:00": 0.0, "2020-04-13 00:00:00": -0.0012986114155760186, "2020-04-14 00:00:00": 0.008800880088008823, "2020-04-15 00:00:00": -0.0032715376226826504, "2020-04-16 00:00:00": 0.0010940919037198593, "2020-04-17 00:00:00": -0.008743169398907126, "2020-04-20 00:00:00": 0.0187431091510474, "2020-04-21 00:00:00": -0.01082251082251074, "2020-04-22 00:00:00": 0.015317286652078627, "2020-04-23 00:00:00": 0.011853448275862228, "2020-04-24 00:00:00": 0.0021299254526090552, "2020-04-27 00:00:00": 0.002125398512221083, "2020-04-28 00:00:00": 0.0021208907741250295, "2020-04-29 00:00:00": 0.01375661375661381, "2020-04-30 00:00:00": 0.0, "2020-05-06 00:00:00": 0.02713987473903963, "2020-05-07 00:00:00": -0.009146341463414606, "2020-05-08 00:00:00": 0.013333333333333525, "2020-05-11 00:00:00": 0.02834008097165978, "2020-05-12 00:00:00": 0.0, "2020-05-13 00:00:00": -0.014763779527559143, "2020-05-14 00:00:00": 0.002997002997002988, "2020-05-15 00:00:00": -0.011952191235059724, "2020-05-18 00:00:00": -0.029233870967741753, "2020-05-19 00:00:00": 0.010384215991692546, "2020-05-20 00:00:00": -0.0010277492291880283, "2020-05-21 00:00:00": 0.024691358024691277, "2020-05-22 00:00:00": 0.016064257028112355, "2020-05-25 00:00:00": 0.01877470355731242, "2020-05-26 00:00:00": 0.0019398642095052403, "2020-05-27 00:00:00": 0.013552758954501453, "2020-05-28 00:00:00": -0.0019102196752626927, "2020-05-29 00:00:00": -0.010526315789473694, "2020-06-01 00:00:00": 0.04061895551257254, "2020-06-02 00:00:00": -0.01301115241635688, "2020-06-03 00:00:00": -0.00941619585687388, "2020-06-04 00:00:00": 0.013307984790874658, "2020-06-05 00:00:00": -0.0009380863039399169, "2020-06-08 00:00:00": 0.0, "2020-06-09 00:00:00": 0.032863849765258156, "2020-06-10 00:00:00": 0.016363636363636313, "2020-06-11 00:00:00": -0.002683363148479419, "2020-06-12 00:00:00": 0.022421524663677143, "2020-06-15 00:00:00": 0.023684210526315835, "2020-06-16 00:00:00": -0.01199657240788358, "2020-06-17 00:00:00": -0.0008673026886382928, "2020-06-18 00:00:00": 0.017361111111111216, "2020-06-19 00:00:00": 0.011945392491467461, "2020-06-22 00:00:00": 0.016863406408094538, "2020-06-23 00:00:00": 0.04311774461028186, "2020-06-24 00:00:00": 0.008744038155802762, "2020-06-29 00:00:00": -0.018124507486209487, "2020-06-30 00:00:00": 0.056982343499197396, "2020-07-01 00:00:00": 0.01822323462414573, "2020-07-02 00:00:00": 0.03504847129008226, "2020-07-03 00:00:00": -0.018731988472622745, "2020-07-06 00:00:00": 0.090308370044053, "2020-07-07 00:00:00": 0.08484848484848477, "2020-07-08 00:00:00": -0.01613904407200486, "2020-07-09 00:00:00": -0.006940063091482742, "2020-07-10 00:00:00": -0.010800508259212107, "2020-07-13 00:00:00": 0.06422607578676946, "2020-07-14 00:00:00": -0.015087507543753778, "2020-07-15 00:00:00": 0.022671568627450966, "2020-07-16 00:00:00": -0.024565608148592002, "2020-07-17 00:00:00": -0.05589680589680594, "2020-07-20 00:00:00": 0.03448275862068967, "2020-07-21 00:00:00": 0.025157232704402666, "2020-07-22 00:00:00": -0.028220858895705653, "2020-07-23 00:00:00": -0.006313131313131351, "2020-07-24 00:00:00": -0.03557814485387548, "2020-07-27 00:00:00": 0.07641633728590257, "2020-07-28 00:00:00": -0.002447980416156552, "2020-07-29 00:00:00": 0.0067484662576686345, "2020-07-30 00:00:00": -0.013406459475929325, "2020-07-31 00:00:00": -0.01605929586164305, "2020-08-03 00:00:00": 0.030131826741996312, "2020-08-04 00:00:00": -0.024984765386959203, "2020-08-05 00:00:00": -0.0024999999999998786, "2020-08-06 00:00:00": 0.0764411027568921, "2020-08-07 00:00:00": -0.009313154831199093, "2020-08-10 00:00:00": -0.05816686251468858, "2020-08-11 00:00:00": -0.014971927635683217, "2020-08-12 00:00:00": 0.05256491450285004, "2020-08-13 00:00:00": 0.05776173285198554, "2020-08-14 00:00:00": -0.011376564277588236, "2020-08-17 00:00:00": 0.001726121979286688, "2020-08-18 00:00:00": 0.05514072372199883, "2020-08-19 00:00:00": -0.07729994556341872, "2020-08-20 00:00:00": 0.0023598820058997514, "2020-08-21 00:00:00": 0.01942319011183059, "2020-08-24 00:00:00": 0.01212471131639719, "2020-08-25 00:00:00": -0.00798630918425564, "2020-08-26 00:00:00": -0.003450258769407695, "2020-08-27 00:00:00": 0.05424120023081366, "2020-08-28 00:00:00": 0.03940886699507377, "2020-08-31 00:00:00": 0.006319115323854785, "2020-09-01 00:00:00": 0.010465724751438957, "2020-09-02 00:00:00": -0.0414293112377005, "2020-09-03 00:00:00": -0.058346839546191354, "2020-09-04 00:00:00": -0.013769363166953485, "2020-09-07 00:00:00": -0.016870273414776083, "2020-09-08 00:00:00": 0.012292079230769115, "2020-09-09 00:00:00": 0.0, "2020-09-10 00:00:00": 0.0, "2020-09-11 00:00:00": 0.0, "2020-09-14 00:00:00": 0.0, "2020-09-15 00:00:00": 0.0, "2020-09-16 00:00:00": 0.0, "2020-09-17 00:00:00": 0.0, "2020-09-18 00:00:00": 0.0, "2020-09-21 00:00:00": 0.0, "2020-09-22 00:00:00": 0.0, "2020-09-23 00:00:00": 0.0, "2020-09-24 00:00:00": 0.0, "2020-09-25 00:00:00": 0.0, "2020-09-28 00:00:00": 0.0, "2020-09-29 00:00:00": 0.0, "2020-09-30 00:00:00": 0.0, "2020-10-09 00:00:00": 0.0, "2020-10-12 00:00:00": 0.0, "2020-10-13 00:00:00": 0.0, "2020-10-14 00:00:00": -0.0012986114155761476, "2020-10-15 00:00:00": -0.08723281340265736, "2020-10-16 00:00:00": -0.058187691772152085, "2020-10-19 00:00:00": 0.0, "2020-10-20 00:00:00": 0.0, "2020-10-21 00:00:00": 0.0, "2020-10-22 00:00:00": 0.0, "2020-10-23 00:00:00": 0.0, "2020-10-26 00:00:00": 0.0, "2020-10-27 00:00:00": 0.0, "2020-10-28 00:00:00": 0.0, "2020-10-29 00:00:00": 0.0, "2020-10-30 00:00:00": -0.0012986114155762924, "2020-11-02 00:00:00": -0.018541409147095265, "2020-11-03 00:00:00": 0.051637279596977385, "2020-11-04 00:00:00": 0.01976047904191617, "2020-11-05 00:00:00": -0.0023487962419262006, "2020-11-06 00:00:00": -0.010594467333725596, "2020-11-09 00:00:00": 0.015466983938132138, "2020-11-10 00:00:00": 0.008201523140011754, "2020-11-11 00:00:00": -0.0290528762347472, "2020-11-12 00:00:00": 0.014362657091561825, "2020-11-13 00:00:00": 0.002359882005899539, "2020-11-16 00:00:00": 0.013537374926427464, "2020-11-17 00:00:00": 0.0, "2020-11-18 00:00:00": 0.011614401858304353, "2020-11-19 00:00:00": 0.006888633754305255, "2020-11-20 00:00:00": 0.013683010262257933, "2020-11-23 00:00:00": 0.019122609673790696, "2020-11-24 00:00:00": -0.01821192052980149, "2020-11-25 00:00:00": 0.0, "2020-11-26 00:00:00": 0.0, "2020-11-27 00:00:00": -0.026981450252951057, "2020-11-30 00:00:00": -0.05430387059503169, "2020-12-01 00:00:00": -0.03485407782529019, "2020-12-02 00:00:00": 0.0, "2020-12-03 00:00:00": 0.0, "2020-12-04 00:00:00": 0.0, "2020-12-07 00:00:00": 0.0, "2020-12-08 00:00:00": 0.0, "2020-12-09 00:00:00": 0.0, "2020-12-10 00:00:00": 0.0, "2020-12-11 00:00:00": 0.0, "2020-12-14 00:00:00": 0.0, "2020-12-15 00:00:00": 0.0, "2020-12-16 00:00:00": 0.0, "2020-12-17 00:00:00": 0.0, "2020-12-18 00:00:00": 0.0, "2020-12-21 00:00:00": 0.0, "2020-12-22 00:00:00": 0.0, "2020-12-23 00:00:00": 0.0, "2020-12-24 00:00:00": 0.0, "2020-12-25 00:00:00": -0.0012986114155761886, "2020-12-28 00:00:00": -0.019907100199071045, "2020-12-29 00:00:00": -0.015572105619499064, "2020-12-30 00:00:00": 0.028198074277854188, "2020-12-31 00:00:00": 0.03745819397993322}}, "daily_values": {"value": {"2020-01-02 00:00:00": 100000.0, "2020-01-03 00:00:00": 100000.0, "2020-01-06 00:00:00": 100000.0, "2020-01-07 00:00:00": 100000.0, "2020-01-08 00:00:00": 100000.0, "2020-01-09 00:00:00": 100000.0, "2020-01-10 00:00:00": 100000.0, "2020-01-13 00:00:00": 100000.0, "2020-01-14 00:00:00": 100000.0, "2020-01-15 00:00:00": 100000.0, "2020-01-16 00:00:00": 100000.0, "2020-01-17 00:00:00": 100000.0, "2020-01-20 00:00:00": 100000.0, "2020-01-21 00:00:00": 100000.0, "2020-01-22 00:00:00": 100000.0, "2020-01-23 00:00:00": 100000.0, "2020-02-03 00:00:00": 100000.0, "2020-02-04 00:00:00": 100000.0, "2020-02-05 00:00:00": 100000.0, "2020-02-06 00:00:00": 100000.0, "2020-02-07 00:00:00": 100000.0, "2020-02-10 00:00:00": 100000.0, "2020-02-11 00:00:00": 100000.0, "2020-02-12 00:00:00": 100000.0, "2020-02-13 00:00:00": 100000.0, "2020-02-14 00:00:00": 100000.0, "2020-02-17 00:00:00": 100000.0, "2020-02-18 00:00:00": 100000.0, "2020-02-19 00:00:00": 100000.0, "2020-02-20 00:00:00": 100000.0, "2020-02-21 00:00:00": 99870.13885844238, "2020-02-24 00:00:00": 100481.58868818794, "2020-02-25 00:00:00": 101908.30495759426, "2020-02-26 00:00:00": 100073.95546835757, "2020-02-27 00:00:00": 100379.68038323034, "2020-02-28 00:00:00": 94468.99869568988, "2020-03-02 00:00:00": 99156.78072373923, "2020-03-03 00:00:00": 98647.23919895124, "2020-03-04 00:00:00": 99462.505638612, "2020-03-05 00:00:00": 100889.22190801833, "2020-03-06 00:00:00": 101194.9468228911, "2020-03-09 00:00:00": 97933.88106424808, "2020-03-10 00:00:00": 99156.78072373923, "2020-03-11 00:00:00": 99156.78072373923, "2020-03-12 00:00:00": 97195.94127062704, "2020-03-13 00:00:00": 97195.94127062704, "2020-03-16 00:00:00": 97195.94127062704, "2020-03-17 00:00:00": 97195.94127062704, "2020-03-18 00:00:00": 97195.94127062704, "2020-03-19 00:00:00": 97195.94127062704, "2020-03-20 00:00:00": 97195.94127062704, "2020-03-23 00:00:00": 97195.94127062704, "2020-03-24 00:00:00": 97195.94127062704, "2020-03-25 00:00:00": 97195.94127062704, "2020-03-26 00:00:00": 97195.94127062704, "2020-03-27 00:00:00": 97195.94127062704, "2020-03-30 00:00:00": 97195.94127062704, "2020-03-31 00:00:00": 97195.94127062704, "2020-04-01 00:00:00": 97195.94127062704, "2020-04-02 00:00:00": 97195.94127062704, "2020-04-03 00:00:00": 97195.94127062704, "2020-04-07 00:00:00": 97195.94127062704, "2020-04-08 00:00:00": 97195.94127062704, "2020-04-09 00:00:00": 97195.94127062704, "2020-04-10 00:00:00": 97195.94127062704, "2020-04-13 00:00:00": 97069.72151174535, "2020-04-14 00:00:00": 97924.02049094663, "2020-04-15 00:00:00": 97603.65837374615, "2020-04-16 00:00:00": 97710.4457461463, "2020-04-17 00:00:00": 96856.14676694502, "2020-04-20 00:00:00": 98671.53209774774, "2020-04-21 00:00:00": 97603.65837374615, "2020-04-22 00:00:00": 99098.68158734837, "2020-04-23 00:00:00": 100273.34268375015, "2020-04-24 00:00:00": 100486.91742855046, "2020-04-27 00:00:00": 100700.49217335078, "2020-04-28 00:00:00": 100914.06691815109, "2020-04-29 00:00:00": 102302.30275935317, "2020-04-30 00:00:00": 102302.30275935317, "2020-05-06 00:00:00": 105078.77444175733, "2020-05-07 00:00:00": 104117.68809015589, "2020-05-08 00:00:00": 105505.92393135799, "2020-05-11 00:00:00": 108495.97035856245, "2020-05-12 00:00:00": 108495.97035856245, "2020-05-13 00:00:00": 106894.15977256004, "2020-05-14 00:00:00": 107214.52188976052, "2020-05-15 00:00:00": 105933.07342095861, "2020-05-18 00:00:00": 102836.23962135399, "2020-05-19 00:00:00": 103904.11334535558, "2020-05-20 00:00:00": 103797.32597295543, "2020-05-21 00:00:00": 106360.22291055926, "2020-05-22 00:00:00": 108068.8208689618, "2020-05-25 00:00:00": 110097.78094456486, "2020-05-26 00:00:00": 110311.35568936517, "2020-05-27 00:00:00": 111806.37890296741, "2020-05-28 00:00:00": 111592.80415816708, "2020-05-29 00:00:00": 110418.14306176532, "2020-06-01 00:00:00": 114903.21270257204, "2020-06-02 00:00:00": 113408.1894889698, "2020-06-03 00:00:00": 112340.3157649682, "2020-06-04 00:00:00": 113835.33897857045, "2020-06-05 00:00:00": 113728.5516061703, "2020-06-08 00:00:00": 113728.5516061703, "2020-06-09 00:00:00": 117466.10964017588, "2020-06-10 00:00:00": 119388.28234337876, "2020-06-11 00:00:00": 119067.92022617828, "2020-06-12 00:00:00": 121737.60453618228, "2020-06-15 00:00:00": 124620.8635909866, "2020-06-16 00:00:00": 123125.84037738435, "2020-06-17 00:00:00": 123019.05300498419, "2020-06-18 00:00:00": 125154.8004529874, "2020-06-19 00:00:00": 126649.82366658963, "2020-06-22 00:00:00": 128785.57111459284, "2020-06-23 00:00:00": 134338.51447940114, "2020-06-24 00:00:00": 135513.1755758029, "2020-06-29 00:00:00": 133057.06601059923, "2020-06-30 00:00:00": 140638.96945101058, "2020-07-01 00:00:00": 143201.8663886144, "2020-07-02 00:00:00": 148220.87289142195, "2020-07-03 00:00:00": 145444.40120901776, "2020-07-06 00:00:00": 158579.24801423744, "2020-07-07 00:00:00": 172034.45693665757, "2020-07-08 00:00:00": 169257.98525425344, "2020-07-09 00:00:00": 168083.32415785166, "2020-07-10 00:00:00": 166267.93882704896, "2020-07-13 00:00:00": 176946.67606706495, "2020-07-14 00:00:00": 174276.99175706095, "2020-07-15 00:00:00": 178228.12453586687, "2020-07-16 00:00:00": 173849.8422674603, "2020-07-17 00:00:00": 164132.19137904575, "2020-07-20 00:00:00": 169791.92211625422, "2020-07-21 00:00:00": 174063.41701226064, "2020-07-22 00:00:00": 169151.19788185327, "2020-07-23 00:00:00": 168083.32415785166, "2020-07-24 00:00:00": 162103.2313034427, "2020-07-27 00:00:00": 174490.56650186126, "2020-07-28 00:00:00": 174063.41701226064, "2020-07-29 00:00:00": 175238.0781086624, "2020-07-30 00:00:00": 172888.75591585887, "2020-07-31 00:00:00": 170112.2842334547, "2020-08-03 00:00:00": 175238.0781086624, "2020-08-04 00:00:00": 170859.79584025583, "2020-08-05 00:00:00": 170432.6463506552, "2020-08-06 00:00:00": 183460.7057834747, "2020-08-07 00:00:00": 181752.10782507213, "2020-08-10 00:00:00": 171180.1579574563, "2020-08-11 00:00:00": 168617.26101985245, "2020-08-12 00:00:00": 177480.61292906574, "2020-08-13 00:00:00": 187732.2006794811, "2020-08-14 00:00:00": 185596.45323147788, "2020-08-17 00:00:00": 185916.81534867838, "2020-08-18 00:00:00": 196168.40309909373, "2020-08-19 00:00:00": 181004.596218271, "2020-08-20 00:00:00": 181431.74570787165, "2020-08-21 00:00:00": 184955.72899707695, "2020-08-24 00:00:00": 187198.2638174803, "2020-08-25 00:00:00": 185703.24060387805, "2020-08-26 00:00:00": 185062.5163694771, "2020-08-27 00:00:00": 195100.52937509213, "2020-08-28 00:00:00": 202789.22018790361, "2020-08-31 00:00:00": 204070.66865670556, "2020-09-01 00:00:00": 206206.41610470874, "2020-09-02 00:00:00": 197663.42631269598, "2020-09-03 00:00:00": 186130.3900934787, "2020-09-04 00:00:00": 183567.49315587486, "2020-09-07 00:00:00": 180470.65935627022, "2020-09-08 00:00:00": 182689.01899990664, "2020-09-09 00:00:00": 182689.01899990664, "2020-09-10 00:00:00": 182689.01899990664, "2020-09-11 00:00:00": 182689.01899990664, "2020-09-14 00:00:00": 182689.01899990664, "2020-09-15 00:00:00": 182689.01899990664, "2020-09-16 00:00:00": 182689.01899990664, "2020-09-17 00:00:00": 182689.01899990664, "2020-09-18 00:00:00": 182689.01899990664, "2020-09-21 00:00:00": 182689.01899990664, "2020-09-22 00:00:00": 182689.01899990664, "2020-09-23 00:00:00": 182689.01899990664, "2020-09-24 00:00:00": 182689.01899990664, "2020-09-25 00:00:00": 182689.01899990664, "2020-09-28 00:00:00": 182689.01899990664, "2020-09-29 00:00:00": 182689.01899990664, "2020-09-30 00:00:00": 182689.01899990664, "2020-10-09 00:00:00": 182689.01899990664, "2020-10-12 00:00:00": 182689.01899990664, "2020-10-13 00:00:00": 182689.01899990664, "2020-10-14 00:00:00": 182451.77695433295, "2020-10-15 00:00:00": 166535.99514029236, "2020-10-16 00:00:00": 156845.6499861004, "2020-10-19 00:00:00": 156845.6499861004, "2020-10-20 00:00:00": 156845.6499861004, "2020-10-21 00:00:00": 156845.6499861004, "2020-10-22 00:00:00": 156845.6499861004, "2020-10-23 00:00:00": 156845.6499861004, "2020-10-26 00:00:00": 156845.6499861004, "2020-10-27 00:00:00": 156845.6499861004, "2020-10-28 00:00:00": 156845.6499861004, "2020-10-29 00:00:00": 156845.6499861004, "2020-10-30 00:00:00": 156641.96843454498, "2020-11-02 00:00:00": 153737.6056081937, "2020-11-03 00:00:00": 161676.19733355384, "2020-11-04 00:00:00": 164870.99644254023, "2020-11-05 00:00:00": 164483.74806569336, "2020-11-06 00:00:00": 162741.13036988262, "2020-11-09 00:00:00": 165258.24481938707, "2020-11-10 00:00:00": 166613.614138351, "2020-11-11 00:00:00": 161773.00942776556, "2020-11-12 00:00:00": 164096.49968884655, "2020-11-13 00:00:00": 164483.74806569336, "2020-11-16 00:00:00": 166710.4262325627, "2020-11-17 00:00:00": 166710.4262325627, "2020-11-18 00:00:00": 168646.66811679688, "2020-11-19 00:00:00": 169808.41324733736, "2020-11-20 00:00:00": 172131.9035084184, "2020-11-23 00:00:00": 175423.5147116165, "2020-11-24 00:00:00": 172228.71560263008, "2020-11-25 00:00:00": 172228.71560263008, "2020-11-26 00:00:00": 172228.71560263008, "2020-11-27 00:00:00": 167581.73508046806, "2020-11-30 00:00:00": 158481.39822456744, "2020-12-01 00:00:00": 152957.67523698756, "2020-12-02 00:00:00": 152957.67523698756, "2020-12-03 00:00:00": 152957.67523698756, "2020-12-04 00:00:00": 152957.67523698756, "2020-12-07 00:00:00": 152957.67523698756, "2020-12-08 00:00:00": 152957.67523698756, "2020-12-09 00:00:00": 152957.67523698756, "2020-12-10 00:00:00": 152957.67523698756, "2020-12-11 00:00:00": 152957.67523698756, "2020-12-14 00:00:00": 152957.67523698756, "2020-12-15 00:00:00": 152957.67523698756, "2020-12-16 00:00:00": 152957.67523698756, "2020-12-17 00:00:00": 152957.67523698756, "2020-12-18 00:00:00": 152957.67523698756, "2020-12-21 00:00:00": 152957.67523698756, "2020-12-22 00:00:00": 152957.67523698756, "2020-12-23 00:00:00": 152957.67523698756, "2020-12-24 00:00:00": 152957.67523698756, "2020-12-25 00:00:00": 152759.0426538248, "2020-12-28 00:00:00": 149718.05308540096, "2020-12-29 00:00:00": 147386.62774960932, "2020-12-30 00:00:00": 151542.64682645525, "2020-12-31 00:00:00": 157219.16068751313}}, "max_drawdown": 0.0, "metrics": {"annual_return": 0.5741471364267343, "avg_holding_period": "0 days 00:00:00", "avg_loss": -0.01, "avg_trade": 0.005, "avg_trade_duration": "0 days", "avg_win": 0.01, "best_trade": 0.02, "calmar_ratio": 0.0, "daily_return": 0.001571954963942668, "end_date": "2020-12-31 00:00:00", "expectancy": 0.0, "final_equity": 157219.16068751313, "initial_capital": 100000.0, "max_daily_loss": -0.08723281340265736, "max_daily_profit": 0.090308370044053, "max_drawdown": 0.0, "max_drawdown_duration": "0 days", "max_loss_streak": 2.0, "max_trade_duration": "0 days", "max_win_streak": 1.0, "net_profit": 57219.160687513126, "num_trades": 0.0, "omega_ratio": 1.0, "profit_factor": 1.0, "return_volatility_ratio": 0.0, "sharpe_ratio": 1.54971492427584, "sortino_ratio": 1.7834137476434087, "start_date": "2020-01-02 00:00:00", "total_return": 0.5721916068751312, "volatility_annual": 0.35758004762435874, "win_rate": 0.5, "worst_trade": -0.02}, "monthly_returns": {"monthly_return": {"2020-01-31 00:00:00": 0.0, "2020-02-29 00:00:00": -0.05531001304310135, "2020-03-31 00:00:00": 0.028866004854368743, "2020-04-30 00:00:00": 0.05253677696796255, "2020-05-31 00:00:00": 0.07933194154488543, "2020-06-30 00:00:00": 0.27369439071566704, "2020-07-31 00:00:00": 0.20956719817767677, "2020-08-31 00:00:00": 0.19962335216572558, "2020-09-30 00:00:00": -0.10477571224489812, "2020-10-31 00:00:00": -0.14257589595669662, "2020-11-30 00:00:00": 0.011742892459826626, "2020-12-31 00:00:00": -0.007964578500662345}}, "report_date": "2025-05-25 17:17:53", "sharpe_ratio": 1.54971492427584, "strategy": "dual_ma", "symbol": "sh603768", "trades": [{"Avg Entry Price": 9.8098, "Avg Exit Price": 9.54045, "Column": 0, "Direction": "Long", "Entry Fees": 29.9910026992, "Entry Timestamp": "2020-02-21T00:00:00.000", "Exit Fees": 29.167532641, "Exit Timestamp": "2020-03-12T00:00:00.000", "Exit Trade Id": 0, "PnL": -2804.058729373, "Position Id": 0, "Return": -0.0280489995, "Size": 10190.8304957594, "Status": "Closed"}, {"Avg Entry Price": 9.09909, "Avg Exit Price": 17.11287, "Column": 0, "Direction": "Long", "Entry Fees": 29.15003737, "Entry Timestamp": "2020-04-13T00:00:00.000", "Exit Fees": 54.8231526458, "Exit Timestamp": "2020-09-08T00:00:00.000", "Exit Trade Id": 1, "PnL": 85493.0777292796, "Position Id": 1, "Return": 0.8798590202, "Size": 10678.737240016, "Status": "Closed"}, {"Avg Entry Price": 17.32731, "Avg Exit Price": 14.8851, "Column": 0, "Direction": "Long", "Entry Fees": 54.7902686194, "Entry Timestamp": "2020-10-14T00:00:00.000", "Exit Fees": 47.0678153404, "Exit Timestamp": "2020-10-16T00:00:00.000", "Exit Trade Id": 2, "PnL": -25843.3690138062, "Position Id": 2, "Return": -0.1415034257, "Size": 10540.2528569805, "Status": "Closed"}, {"Avg Entry Price": 16.19618, "Avg Exit Price": 15.80418, "Column": 0, "Direction": "Long", "Entry Fees": 47.0395831209, "Entry Timestamp": "2020-10-30T00:00:00.000", "Exit Fees": 45.901072893, "Exit Timestamp": "2020-12-01T00:00:00.000", "Exit Trade Id": 3, "PnL": -3887.9747491128, "Position Id": 3, "Return": -0.0247959771, "Size": 9681.2094211709, "Status": "Closed"}, {"Avg Entry Price": 15.08507, "Avg Exit Price": 15.51, "Column": 0, "Direction": "Long", "Entry Fees": 45.8735405089, "Entry Timestamp": "2020-12-25T00:00:00.000", "Exit Fees": 0.0, "Exit Timestamp": "2020-12-31T00:00:00.000", "Exit Trade Id": 4, "PnL": 4261.4854505256, "Position Id": 4, "Return": 0.0278689114, "Size": 10136.6318947462, "Status": "Open"}], "win_rate": 0.5, "yearly_returns": {"yearly_return": {"2020-12-31 00:00:00": 0.5721916068751309}}};
                    console.log(reportData); // For debugging
                </script>
                <div class="report-meta">
                    <p><strong>回测周期:</strong> 2020-01-02 00:00:00 至 2020-12-31 00:00:00</p>
                    <p><strong>报告生成时间:</strong> 2025-05-25 17:17:53</p>
                    <p><strong>初始资金:</strong> 100000¥</p>
                </div>
            </div>

            <div class="section">
                <h2>绩效概览</h2>
                <p class="section-description">本节展示策略的核心绩效指标，包括收益、风险和交易相关的关键数据。</p>
                <div class="metrics-container">
                    <div class="metric-group">
                        <h3>收益指标</h3>
                        <div class="metric">
                            <span class="metric-name">总收益率</span>
                            <span class="metric-value positive">57.22%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">年化收益率</span>
                            <span class="metric-value positive">57.41%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">日均收益率</span>
                            <span class="metric-value positive">0.1572%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">最终净值</span>
                            <span class="metric-value positive">157219.16¥</span>
                        </div>
                    </div>

                    <div class="metric-group">
                        <h3>风险指标</h3>
                        <div class="metric">
                            <span class="metric-name">最大回撤</span>
                            <span class="metric-value negative">0.0%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">年化波动率</span>
                            <span class="metric-value">35.76%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">夏普比率</span>
                            <span class="metric-value positive">1.55</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">卡玛比率</span>
                            <span class="metric-value negative">0.0</span>
                        </div>
                    </div>

                    <div class="metric-group">
                        <h3>交易指标</h3>
                        <div class="metric">
                            <span class="metric-name">交易次数</span>
                            <span class="metric-value">0.0</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">胜率</span>
                            <span class="metric-value negative">50.0%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">盈亏比</span>
                            <span class="metric-value negative">1.0</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>图表分析</h2>
                <p class="section-description">本节通过多种图表直观展示策略的表现，包括净值曲线、回撤分析、收益分布和交易特征等。</p>
                <div class="chart-container">
                    <div class="chart">
                        <h3>净值曲线 <small>策略资金变化趋势</small></h3>
                        <div id="equity-curve-chart" style="width: 100%;height:400px;" class="chart-box"></div>
                    </div>
                    <div class="chart">
                        <h3>回撤分析 <small>资金回撤情况</small></h3>
                        <div id="drawdowns-chart" style="width: 100%;height:400px;" class="chart-box"></div>
                    </div>
                    <div class="chart">
                        <h3>月度收益热力图 <small>月度表现分布</small></h3>
                        <div id="monthly-returns-heatmap-chart" style="width: 100%;height:400px;" class="chart-box"></div>
                    </div>
                    <div class="chart">
                        <h3>日收益分布 <small>日收益率频率分布</small></h3>
                        <div id="daily-returns-hist-chart" style="width: 100%;height:400px;" class="chart-box"></div>
                    </div>
                    
                    <div class="chart">
                        <h3>交易收益分布 <small>单笔交易收益率分布</small></h3>
                        <div id="trade-returns-hist-chart" style="width: 100%;height:400px;" class="chart-box"></div>
                    </div>
                    <div class="chart">
                        <h3>交易持续时间分布 <small>持仓周期分析</small></h3>
                        <div id="trade-duration-hist-chart" style="width: 100%;height:400px;" class="chart-box"></div>
                    </div>
                    
                </div>
            </div>

            <!-- 更新模板条件判断 -->
            <div class="section">
                <h2>交易明细记录</h2>
                <p class="section-description">本节展示所有交易的详细信息，包括入场/出场时间、价格、方向、持续时间及盈亏情况。</p>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>入场时间</th>
                                <th>出场时间</th>
                                <th>方向</th>
                                <th>持仓时长</th>
                                <th>入场价格</th>
                                <th>出场价格</th>
                                <th>数量</th>
                                <th>收益率(%)</th>
                                <th>盈亏(¥)</th>
                            </tr>
                        </thead>
                        <tbody>
                        
                            
                            <tr class="table-danger">
                                <td>1</td>
                                <td>2020-02-21T00:00:00.000</td>
                                <td>2020-03-12T00:00:00.000</td>
                                <td>多头</td>
                                <td>-</td>
                                <td>-</td>
                                <td>-</td>
                                <td>10190.8304957594</td>
                                <td class="negative">-2.8</td>
                                <td class="negative">-2804.06</td>
                            </tr>
                            
                            <tr class="table-success">
                                <td>2</td>
                                <td>2020-04-13T00:00:00.000</td>
                                <td>2020-09-08T00:00:00.000</td>
                                <td>多头</td>
                                <td>-</td>
                                <td>-</td>
                                <td>-</td>
                                <td>10678.737240016</td>
                                <td class="positive">87.99</td>
                                <td class="positive">85493.08</td>
                            </tr>
                            
                            <tr class="table-danger">
                                <td>3</td>
                                <td>2020-10-14T00:00:00.000</td>
                                <td>2020-10-16T00:00:00.000</td>
                                <td>多头</td>
                                <td>-</td>
                                <td>-</td>
                                <td>-</td>
                                <td>10540.2528569805</td>
                                <td class="negative">-14.15</td>
                                <td class="negative">-25843.37</td>
                            </tr>
                            
                            <tr class="table-danger">
                                <td>4</td>
                                <td>2020-10-30T00:00:00.000</td>
                                <td>2020-12-01T00:00:00.000</td>
                                <td>多头</td>
                                <td>-</td>
                                <td>-</td>
                                <td>-</td>
                                <td>9681.2094211709</td>
                                <td class="negative">-2.48</td>
                                <td class="negative">-3887.97</td>
                            </tr>
                            
                            <tr class="table-success">
                                <td>5</td>
                                <td>2020-12-25T00:00:00.000</td>
                                <td>2020-12-31T00:00:00.000</td>
                                <td>多头</td>
                                <td>-</td>
                                <td>-</td>
                                <td>-</td>
                                <td>10136.6318947462</td>
                                <td class="positive">2.79</td>
                                <td class="positive">4261.49</td>
                            </tr>
                            
                        
                        </tbody>
                        
                        <tfoot>
                            <tr class="table-info">
                                <td colspan="8" class="text-right"><strong>总计:</strong></td>
                                <td class="positive"><strong>57.22</strong></td>
                                <td class="positive"><strong>57219.16</strong></td>
                            </tr>
                        </tfoot>
                        
                    </table>
                </div>
            </div>

            <div class="section">
                <h2>详细指标分析</h2>
                <p class="section-description">本节展示策略的详细绩效指标，包括收益、风险和交易相关的各项数据。</p>
                <div class="metrics-container">
                    <div class="metric-group">
                        <h3>收益指标</h3>
                        
                            
                            <div class="metric">
                                <span class="metric-name">
                                    总收益率
                                    
                                </span>
                                <span class="metric-value positive">
                                    
                                        57.22%
                                    
                                </span>
                            </div>
                            
                        
                            
                            <div class="metric">
                                <span class="metric-name">
                                    年化收益率
                                    
                                </span>
                                <span class="metric-value positive">
                                    
                                        57.41%
                                    
                                </span>
                            </div>
                            
                        
                            
                            <div class="metric">
                                <span class="metric-name">
                                    日均收益率
                                    
                                </span>
                                <span class="metric-value positive">
                                    
                                        0.16%
                                    
                                </span>
                            </div>
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                            <div class="metric">
                                <span class="metric-name">
                                    单日最大收益
                                    
                                </span>
                                <span class="metric-value positive">
                                    
                                        9.03%
                                    
                                </span>
                            </div>
                            
                        
                            
                            <div class="metric">
                                <span class="metric-name">
                                    单日最大亏损
                                    
                                </span>
                                <span class="metric-value negative">
                                    
                                        -8.72%
                                    
                                </span>
                            </div>
                            
                        
                    </div>

                    <div class="metric-group">
                        <h3>风险指标</h3>
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                            <div class="metric">
                                <span class="metric-name">
                                    最大回撤
                                    
                                </span>
                                <span class="metric-value negative">
                                    
                                        0.0%
                                    
                                </span>
                            </div>
                            
                        
                            
                            <div class="metric">
                                <span class="metric-name">
                                    最长回撤期
                                    
                                </span>
                                <span class="metric-value negative">
                                    
                                        0 days
                                    
                                </span>
                            </div>
                            
                        
                            
                        
                            
                        
                            
                        
                            
                            <div class="metric">
                                <span class="metric-name">
                                    夏普比率
                                    
                                </span>
                                <span class="metric-value positive">
                                    
                                        1.55
                                    
                                </span>
                            </div>
                            
                        
                            
                            <div class="metric">
                                <span class="metric-name">
                                    索提诺比率
                                    
                                </span>
                                <span class="metric-value positive">
                                    
                                        1.78
                                    
                                </span>
                            </div>
                            
                        
                            
                            <div class="metric">
                                <span class="metric-name">
                                    卡玛比率
                                    
                                </span>
                                <span class="metric-value negative">
                                    
                                        0.0
                                    
                                </span>
                            </div>
                            
                        
                            
                            <div class="metric">
                                <span class="metric-name">
                                    欧米伽比率
                                    
                                </span>
                                <span class="metric-value positive">
                                    
                                        1.0
                                    
                                </span>
                            </div>
                            
                        
                            
                            <div class="metric">
                                <span class="metric-name">
                                    年化波动率
                                    
                                </span>
                                <span class="metric-value positive">
                                    
                                        35.76%
                                    
                                </span>
                            </div>
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                    </div>

                    <div class="metric-group">
                        <h3>交易指标</h3>
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                            
                            <div class="metric">
                                <span class="metric-name">
                                    胜率
                                    
                                </span>
                                <span class="metric-value positive">
                                    
                                        50.0%
                                    
                                </span>
                            </div>
                            
                        
                            
                            <div class="metric">
                                <span class="metric-name">
                                    盈亏比
                                    
                                </span>
                                <span class="metric-value positive">
                                    
                                        1.0
                                    
                                </span>
                            </div>
                            
                        
                            
                            <div class="metric">
                                <span class="metric-name">
                                    期望值
                                    
                                </span>
                                <span class="metric-value ">
                                    
                                        0.0
                                    
                                </span>
                            </div>
                            
                        
                            
                            <div class="metric">
                                <span class="metric-name">
                                    平均盈利
                                    
                                </span>
                                <span class="metric-value positive">
                                    
                                        0.01
                                    
                                </span>
                            </div>
                            
                        
                            
                            <div class="metric">
                                <span class="metric-name">
                                    平均亏损
                                    
                                </span>
                                <span class="metric-value negative">
                                    
                                        -0.01
                                    
                                </span>
                            </div>
                            
                        
                            
                            <div class="metric">
                                <span class="metric-name">
                                    最佳交易
                                    
                                </span>
                                <span class="metric-value positive">
                                    
                                        0.02
                                    
                                </span>
                            </div>
                            
                        
                            
                            <div class="metric">
                                <span class="metric-name">
                                    最差交易
                                    
                                </span>
                                <span class="metric-value negative">
                                    
                                        -0.02
                                    
                                </span>
                            </div>
                            
                        
                            
                            <div class="metric">
                                <span class="metric-name">
                                    平均交易收益
                                    
                                </span>
                                <span class="metric-value ">
                                    
                                        0.01
                                    
                                </span>
                            </div>
                            
                        
                            
                            <div class="metric">
                                <span class="metric-name">
                                    交易次数
                                    
                                </span>
                                <span class="metric-value ">
                                    
                                        0
                                    
                                </span>
                            </div>
                            
                        
                            
                        
                            
                        
                            
                            <div class="metric">
                                <span class="metric-name">
                                    最长连胜
                                    
                                </span>
                                <span class="metric-value positive">
                                    
                                        1
                                    
                                </span>
                            </div>
                            
                        
                            
                            <div class="metric">
                                <span class="metric-name">
                                    最长连亏
                                    
                                </span>
                                <span class="metric-value ">
                                    
                                        2
                                    
                                </span>
                            </div>
                            
                        
                            
                        
                            
                        
                            
                        
                            
                        
                    </div>
                </div>
            </div>

            <div class="footer">
                <p>由 QuantEngine 量化交易引擎自动生成 | 2025-05-25 17:17:53</p>
                <p class="disclaimer">免责声明：本报告仅供参考，不构成任何投资建议。交易有风险，投资需谨慎。</p>
                <p class="copyright">© 2025 QuantEngine - 版权所有</p>
            </div>

            <!-- 引入ECharts库 -->
            <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>

            <script>
                // 等待文档加载完成
                document.addEventListener('DOMContentLoaded', function() {
                    // 获取图表容器
                    const equityCurveChartDom = document.getElementById('equity-curve-chart');
                    const drawdownsChartDom = document.getElementById('drawdowns-chart');
                    const monthlyReturnsHeatmapChartDom = document.getElementById('monthly-returns-heatmap-chart');
                    const dailyReturnsHistChartDom = document.getElementById('daily-returns-hist-chart');

                    // 检查容器是否存在
                    if (!equityCurveChartDom || !drawdownsChartDom || !monthlyReturnsHeatmapChartDom || !dailyReturnsHistChartDom) {
                        console.error('一个或多个图表容器不存在');
                        return;
                    }

                    // 初始化ECharts实例
                    const equityCurveChart = echarts.init(equityCurveChartDom);
                    const drawdownsChart = echarts.init(drawdownsChartDom);
                    const monthlyReturnsHeatmapChart = echarts.init(monthlyReturnsHeatmapChartDom);
                    const dailyReturnsHistChart = echarts.init(dailyReturnsHistChartDom);

                    // 可选的图表容器
                    const tradeReturnsHistChartDom = document.getElementById('trade-returns-hist-chart');
                    const tradeDurationHistChartDom = document.getElementById('trade-duration-hist-chart');

                    // 如果可选容器存在，则初始化
                    const tradeReturnsHistChart = tradeReturnsHistChartDom ? echarts.init(tradeReturnsHistChartDom) : null;
                    const tradeDurationHistChart = tradeDurationHistChartDom ? echarts.init(tradeDurationHistChartDom) : null;

                // 确保reportData和其子属性存在
                const performance = reportData.performance || {};
                const chart_data = reportData.chart_data || {};
                const advanced = chart_data.advanced || {};
                const risk = chart_data.risk || reportData.risk || {};
                const daily_values = performance.daily_values || {};

                console.log('Chart data structure:', {
                    performance: !!reportData.performance,
                    chart_data: !!reportData.chart_data,
                    advanced: !!advanced,
                    risk: !!risk
                });

                // 净值曲线图表配置
                const equityCurveOption = {
                    title: {
                        text: '净值曲线'
                    },
                    tooltip: {
                        trigger: 'axis',
                        formatter: function (params) {
                            params = params[0];
                            const value = params.value || 0;
                            return params.name + '<br/>净值: ' + (typeof value.toFixed === 'function' ? value.toFixed(4) : '0.0000');
                        }
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: Object.keys(daily_values),
                        axisLabel: {
                            rotate: 45,
                            interval: Math.floor(Object.keys(daily_values).length / 10)
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: '净值',
                        scale: true
                    },
                    series: [
                        {
                            name: '净值',
                            type: 'line',
                            data: Object.values(reportData.performance?.daily_values || {}),
                            smooth: true,
                            showSymbol: false,
                            lineStyle: {
                                width: 2,
                                color: '#3498db'
                            },
                            areaStyle: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                    { offset: 0, color: 'rgba(52, 152, 219, 0.5)' },
                                    { offset: 1, color: 'rgba(52, 152, 219, 0.1)' }
                                ])
                            }
                        }
                    ]
                };

                // 回撤分析图表配置
                const drawdownsOption = {
                    title: {
                        text: '回撤分析'
                    },
                    tooltip: {
                        trigger: 'axis',
                        formatter: function (params) {
                            params = params[0];
                            return params.name + '<br/>回撤: ' + (params.value * 100).toFixed(2) + '%';
                        }
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: Object.keys(risk.drawdowns || {}),
                        axisLabel: {
                            rotate: 45,
                            interval: Math.floor(Object.keys(risk.drawdowns || {}).length / 10)
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: '回撤 (%)',
                        scale: true,
                        axisLabel: {
                            formatter: function (value) {
                                return (value * 100).toFixed(0) + '%';
                            }
                        },
                        inverse: true  // 反转Y轴，使回撤向下显示
                    },
                    series: [
                        {
                            name: '回撤',
                            type: 'line',
                            data: Object.values(risk.drawdowns || {}),
                            smooth: true,
                            showSymbol: false,
                            lineStyle: {
                                width: 2,
                                color: '#e74c3c'
                            },
                            areaStyle: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                    { offset: 0, color: 'rgba(231, 76, 60, 0.1)' },
                                    { offset: 1, color: 'rgba(231, 76, 60, 0.5)' }
                                ])
                            }
                        }
                    ]
                };

                // 月度收益热力图配置
                // 处理月度收益数据
                let monthlyReturnsData = [];
                let years = [];
                const months = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'];

                // 确保advanced.monthly_returns_heatmap存在
                const monthly_returns_heatmap = advanced.monthly_returns_heatmap || {};

                try {
                    if (Object.keys(monthly_returns_heatmap).length > 0) {
                        // 将数据转换为热力图所需格式
                        monthlyReturnsData = Object.entries(monthly_returns_heatmap).map(([date, value]) => {
                            try {
                                // 处理不同的日期格式
                                let year, month;
                                if (date.includes('-')) {
                                    const dateParts = date.split('-');
                                    year = dateParts[0] || new Date().getFullYear().toString();
                                    month = dateParts[1] || '01';
                                } else {
                                    // 如果是其他格式，尝试解析为Date
                                    const dateObj = new Date(date);
                                    year = dateObj.getFullYear().toString();
                                    month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
                                }
                                const numValue = parseFloat(value) || 0;
                                return [month, year, (numValue * 100).toFixed(2)];
                            } catch (e) {
                                console.error('解析月度收益数据出错:', e, 'date:', date, 'value:', value);
                                return ['01', new Date().getFullYear().toString(), '0.00'];
                            }
                        });

                        // 提取年份列表
                        years = Array.from(new Set(monthlyReturnsData.map(item => item[1]))).sort();
                    } else {
                        // 如果没有数据，提供默认数据
                        const currentYear = new Date().getFullYear();
                        years = [currentYear.toString()];
                        // 为每个月创建默认数据
                        months.forEach(month => {
                            monthlyReturnsData.push([month, currentYear.toString(), '0.00']);
                        });
                    }
                } catch (e) {
                    console.error('处理月度收益热力图数据出错:', e);
                    // 提供默认数据
                    const currentYear = new Date().getFullYear();
                    years = [currentYear.toString()];
                    monthlyReturnsData = [];
                    months.forEach(month => {
                        monthlyReturnsData.push([month, currentYear.toString(), '0.00']);
                    });
                }

                const monthlyReturnsHeatmapOption = {
                    title: {
                        text: '月度收益热力图'
                    },
                    tooltip: {
                        position: 'top',
                        formatter: function (params) {
                            return params.value[1] + '-' + params.value[0] + ': ' + params.value[2] + '%';
                        }
                    },
                    grid: {
                        height: '50%',
                        top: '10%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: months,
                        splitArea: {
                            show: true
                        }
                    },
                    yAxis: {
                        type: 'category',
                        data: years,
                        splitArea: {
                            show: true
                        }
                    },
                    visualMap: {
                        min: -10,
                        max: 10,
                        calculable: true,
                        orient: 'horizontal',
                        left: 'center',
                        bottom: '15%',
                        inRange: {
                            color: ['#e74c3c', '#f9f9f9', '#27ae60'] // 红 -> 白 -> 绿
                        }
                    },
                    series: [
                        {
                            name: '月度收益',
                            type: 'heatmap',
                            data: monthlyReturnsData,
                            label: {
                                show: true,
                                formatter: function (params) {
                                    return params.value[2]; // 显示收益率
                                }
                            },
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            }
                        }
                    ]
                };

                // 日收益分布图表配置
                // 处理日收益数据
                let dailyReturnsData = [];
                let dailyReturnsHistData = [];

                // 创建直方图数据处理函数
                function createHistogramData(data, bins = 20) {
                    if (!data || data.length === 0) return [];

                    const min = Math.min(...data);
                    const max = Math.max(...data);
                    const binWidth = (max - min) / bins;

                    const histogram = new Array(bins).fill(0);
                    const binLabels = [];

                    for (let i = 0; i < bins; i++) {
                        const binStart = min + i * binWidth;
                        const binEnd = min + (i + 1) * binWidth;
                        binLabels.push(binStart.toFixed(2) + '~' + binEnd.toFixed(2));
                    }

                    data.forEach(value => {
                        const binIndex = Math.min(Math.floor((value - min) / binWidth), bins - 1);
                        histogram[binIndex]++;
                    });

                    return { histogram, binLabels };
                }

                // 确保advanced.daily_returns存在
                try {
                    if (advanced && advanced.daily_returns) {
                        // 将数据转换为直方图所需格式
                        dailyReturnsData = Object.values(advanced.daily_returns).map(value => {
                            const numValue = parseFloat(value) || 0;
                            return numValue * 100;
                        });

                        const histResult = createHistogramData(dailyReturnsData);
                        dailyReturnsHistData = histResult.histogram;
                        var dailyReturnsLabels = histResult.binLabels;
                    } else {
                        // 如果没有数据，提供默认数据
                        dailyReturnsData = [0, 0.1, -0.1, 0.2, -0.2, 0.15, -0.15];
                        const histResult = createHistogramData(dailyReturnsData);
                        dailyReturnsHistData = histResult.histogram;
                        var dailyReturnsLabels = histResult.binLabels;
                    }
                } catch (e) {
                    console.error('处理日收益数据出错:', e);
                    dailyReturnsData = [0, 0.1, -0.1, 0.2, -0.2, 0.15, -0.15]; // 提供默认数据
                    const histResult = createHistogramData(dailyReturnsData);
                    dailyReturnsHistData = histResult.histogram;
                    var dailyReturnsLabels = histResult.binLabels;
                }

                const dailyReturnsHistOption = {
                    title: {
                        text: '日收益分布'
                    },
                    tooltip: {
                        trigger: 'axis',
                        formatter: function(params) {
                            const param = params[0];
                            return '收益率区间: ' + dailyReturnsLabels[param.dataIndex] + '%<br/>频率: ' + param.value;
                        }
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: dailyReturnsLabels || [],
                        name: '收益率区间 (%)',
                        axisLabel: {
                            rotate: 45,
                            interval: Math.floor((dailyReturnsLabels || []).length / 10)
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: '频率'
                    },
                    series: [
                        {
                            name: '日收益',
                            type: 'bar',
                            data: dailyReturnsHistData || [0], // 确保数据存在
                            itemStyle: {
                                color: '#3498db'
                            },
                            emphasis: {
                                focus: 'series'
                            }
                        }
                    ]
                };

                // 交易收益分布图表配置
                const trades = Array.isArray(reportData.trades) ? reportData.trades : [];
                let tradeReturnsData = [];
                let tradeReturnsHistData = [];
                let tradeReturnsLabels = [];

                try {
                    // 从trades数组中提取收益率数据
                    tradeReturnsData = trades.map(trade => {
                        if (trade && trade.Return !== undefined) {
                            return parseFloat(trade.Return) * 100;
                        } else if (trade && trade.return !== undefined) {
                            return parseFloat(trade.return) * 100;
                        }
                        return null;
                    }).filter(value => value !== null);

                    if (tradeReturnsData.length > 0) {
                        const histResult = createHistogramData(tradeReturnsData);
                        tradeReturnsHistData = histResult.histogram;
                        tradeReturnsLabels = histResult.binLabels;
                    } else {
                        // 提供默认数据
                        tradeReturnsData = [1, -0.5, 2, -1, 0.5];
                        const histResult = createHistogramData(tradeReturnsData);
                        tradeReturnsHistData = histResult.histogram;
                        tradeReturnsLabels = histResult.binLabels;
                    }
                } catch (e) {
                    console.error('处理交易收益数据出错:', e);
                    tradeReturnsData = [1, -0.5, 2, -1, 0.5];
                    const histResult = createHistogramData(tradeReturnsData);
                    tradeReturnsHistData = histResult.histogram;
                    tradeReturnsLabels = histResult.binLabels;
                }

                const tradeReturnsHistOption = {
                    title: {
                        text: '交易收益分布'
                    },
                    tooltip: {
                        trigger: 'axis',
                        formatter: function(params) {
                            const param = params[0];
                            return '收益率区间: ' + tradeReturnsLabels[param.dataIndex] + '%<br/>频率: ' + param.value;
                        }
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: tradeReturnsLabels,
                        name: '收益率区间 (%)',
                        axisLabel: {
                            rotate: 45,
                            interval: Math.floor(tradeReturnsLabels.length / 10)
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: '频率'
                    },
                    series: [
                        {
                            name: '交易收益',
                            type: 'bar',
                            data: tradeReturnsHistData,
                            itemStyle: {
                                color: '#27ae60'
                            },
                            emphasis: {
                                focus: 'series'
                            }
                        }
                    ]
                };

                // 交易持续时间分布图表配置
                let tradeDurationData = [];
                let tradeDurationHistData = [];
                let tradeDurationLabels = [];

                try {
                    tradeDurationData = trades.map(trade => {
                        if (trade && (trade.Duration || trade.duration)) {
                            try {
                                const durationStr = trade.Duration || trade.duration;
                                // 简单的解析，假设格式是 'X days HH:MM:SS' 或 'HH:MM:SS'
                                const parts = durationStr.toString().split(' ');
                                let days = 0;
                                let timeParts;
                                if (parts.length > 1) {
                                    days = parseInt(parts[0] || '0');
                                    timeParts = parts[2] ? parts[2].split(':') : (parts[1] ? parts[1].split(':') : ['0', '0', '0']);
                                } else {
                                    timeParts = parts[0] ? parts[0].split(':') : ['0', '0', '0'];
                                }
                                const hours = parseInt(timeParts[0] || '0');
                                const minutes = parseInt(timeParts[1] || '0');
                                const seconds = parseInt(timeParts[2] || '0');
                                // 转换为总天数
                                return days + hours / 24 + minutes / (24 * 60) + seconds / (24 * 3600);
                            } catch (e) {
                                console.error('解析交易持续时间出错:', e);
                                return null;
                            }
                        }
                        return null;
                    }).filter(value => value !== null);

                    if (tradeDurationData.length > 0) {
                        const histResult = createHistogramData(tradeDurationData);
                        tradeDurationHistData = histResult.histogram;
                        tradeDurationLabels = histResult.binLabels;
                    } else {
                        // 提供默认数据
                        tradeDurationData = [1, 2, 0.5, 3, 1.5, 2.5];
                        const histResult = createHistogramData(tradeDurationData);
                        tradeDurationHistData = histResult.histogram;
                        tradeDurationLabels = histResult.binLabels;
                    }
                } catch (e) {
                    console.error('处理交易持续时间数据出错:', e);
                    tradeDurationData = [1, 2, 0.5, 3, 1.5, 2.5];
                    const histResult = createHistogramData(tradeDurationData);
                    tradeDurationHistData = histResult.histogram;
                    tradeDurationLabels = histResult.binLabels;
                }

                const tradeDurationHistOption = {
                    title: {
                        text: '交易持续时间分布'
                    },
                    tooltip: {
                        trigger: 'axis',
                        formatter: function(params) {
                            const param = params[0];
                            return '持续时间区间: ' + tradeDurationLabels[param.dataIndex] + ' 天<br/>频率: ' + param.value;
                        }
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: tradeDurationLabels,
                        name: '持续时间区间 (天)',
                        axisLabel: {
                            rotate: 45,
                            interval: Math.floor(tradeDurationLabels.length / 10)
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: '频率'
                    },
                    series: [
                        {
                            name: '交易持续时间',
                            type: 'bar',
                            data: tradeDurationHistData,
                            itemStyle: {
                                color: '#f39c12'
                            },
                            emphasis: {
                                focus: 'series'
                            }
                        }
                    ]
                };

                // 使用配置项和数据显示图表 - 增强版本
                // 检查图表容器是否存在，然后再渲染
                try {
                    if (document.getElementById('equity-curve-chart')) {
                        equityCurveChart.setOption(equityCurveOption);
                    }
                } catch (e) {
                    console.error('渲染净值曲线图表出错:', e);
                }

                // 检查是否有回撤数据再渲染
                try {
                    if (document.getElementById('drawdowns-chart')) {
                        drawdownsChart.setOption(drawdownsOption);
                    }
                } catch (e) {
                    console.error('渲染回撤分析图表出错:', e);
                }

                // 检查是否有月度收益数据再渲染
                try {
                    if (document.getElementById('monthly-returns-heatmap-chart') && monthlyReturnsData && monthlyReturnsData.length > 0) {
                        monthlyReturnsHeatmapChart.setOption(monthlyReturnsHeatmapOption);
                    }
                } catch (e) {
                    console.error('渲染月度收益热力图出错:', e);
                }

                // 检查是否有日收益数据再渲染
                try {
                    if (document.getElementById('daily-returns-hist-chart') && dailyReturnsHistData && dailyReturnsHistData.length > 0) {
                        dailyReturnsHistChart.setOption(dailyReturnsHistOption);
                    }
                } catch (e) {
                    console.error('渲染日收益分布图表出错:', e);
                }

                // 检查是否有交易收益数据再渲染
                try {
                    if (document.getElementById('trade-returns-hist-chart') && tradeReturnsHistChart && tradeReturnsHistData && tradeReturnsHistData.length > 0) {
                        tradeReturnsHistChart.setOption(tradeReturnsHistOption);
                    }
                } catch (e) {
                    console.error('渲染交易收益分布图表出错:', e);
                }

                // 检查是否有交易持续时间数据再渲染
                try {
                    if (document.getElementById('trade-duration-hist-chart') && tradeDurationHistChart && tradeDurationHistData && tradeDurationHistData.length > 0) {
                        tradeDurationHistChart.setOption(tradeDurationHistOption);
                    }
                } catch (e) {
                    console.error('渲染交易持续时间分布图表出错:', e);
                }

                // 响应窗口大小变化 - 增强版本
                window.addEventListener('resize', function () {
                    // 只有在图表容器存在时才调整大小
                    try {
                        if (document.getElementById('equity-curve-chart')) {
                            equityCurveChart.resize();
                        }
                    } catch (e) {}

                    try {
                        if (document.getElementById('drawdowns-chart')) {
                            drawdownsChart.resize();
                        }
                    } catch (e) {}

                    try {
                        if (document.getElementById('monthly-returns-heatmap-chart')) {
                            monthlyReturnsHeatmapChart.resize();
                        }
                    } catch (e) {}

                    try {
                        if (document.getElementById('daily-returns-hist-chart')) {
                            dailyReturnsHistChart.resize();
                        }
                    } catch (e) {}

                    try {
                        if (document.getElementById('trade-returns-hist-chart') && tradeReturnsHistChart) {
                            tradeReturnsHistChart.resize();
                        }
                    } catch (e) {}

                    try {
                        if (document.getElementById('trade-duration-hist-chart') && tradeDurationHistChart) {
                            tradeDurationHistChart.resize();
                        }
                    } catch (e) {}
                });
            });
            </script>
        </body>
        </html>
        