"""
日志模块
"""
import logging
import os
import sys
from datetime import datetime


def setup_logger(name, log_level=logging.INFO, log_file=None):
    """
    设置日志记录器
    
    参数:
        name (str): 日志记录器名称
        log_level (int): 日志级别，默认为INFO
        log_file (str): 日志文件路径，默认为None（仅控制台输出）
        
    返回:
        logging.Logger: 日志记录器实例
    """
    # 创建日志记录器
    logger = logging.getLogger(name)
    logger.setLevel(log_level)
    
    # 设置日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 添加控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 如果指定了日志文件，添加文件处理器
    if log_file:
        # 确保日志目录存在
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)
            
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


def get_default_logger():
    """
    获取默认日志记录器
    
    返回:
        logging.Logger: 默认日志记录器实例
    """
    # 创建日志目录
    log_dir = os.path.join(os.getcwd(), 'logs')
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 创建日志文件名，包含当前日期
    current_date = datetime.now().strftime('%Y-%m-%d')
    log_file = os.path.join(log_dir, f'quantengine_{current_date}.log')
    
    # 设置并返回日志记录器
    return setup_logger('quantengine', log_file=log_file)
