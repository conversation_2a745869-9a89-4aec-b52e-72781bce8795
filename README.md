# QuantEngine - 量化交易回测引擎

QuantEngine 是一个用于量化交易策略回测的 Python 框架，提供了完整的数据获取、策略开发、回测和报告生成功能。

## 特点

- 支持多种数据源（Akshare、数据库、模拟数据）
- 灵活的策略开发框架
- 强大的回测引擎
- 丰富的绩效指标和图表
- 支持生成 JSON、HTML 和 PDF 格式的报告

## 安装

### 方法 1: 开发模式安装

```bash
# 克隆仓库
git clone https://github.com/yourusername/quantengine.git
cd quantengine

# 安装依赖
pip install -r requirements.txt

# 以开发模式安装
pip install -e .
```

### 方法 2: 直接安装

```bash
# 克隆仓库
git clone https://github.com/yourusername/quantengine.git
cd quantengine

# 安装包
pip install .
```

## 项目结构

```
quantengine/
├── data/                # 数据源模块
│   ├── data_source.py   # 数据源基类
│   ├── akshare_source.py # Akshare数据源
│   ├── db_source.py     # 数据库数据源
│   └── mock_source.py   # 模拟数据源
├── strategies/          # 策略模块
│   ├── base_strategy.py # 策略基类
│   ├── ma_cross.py      # 均线交叉策略
│   └── rsi_strategy.py  # RSI策略
├── backtest/            # 回测模块
│   ├── engine.py        # 回测引擎
│   └── report.py        # 报告生成器
├── utils/               # 工具模块
│   ├── indicators.py    # 技术指标
│   └── logger.py        # 日志工具
└── main.py              # 主程序入口
```

## 使用方法

### 命令行使用

```bash
python main.py --symbol sh000001 --start 2020-01-01 --end 2021-01-01 --strategy ma_cross --params '{"fast_window": 5, "slow_window": 20}' --data-source akshare --output ./output
```

参数说明：
- `--symbol`: 证券代码
- `--start`: 回测开始日期
- `--end`: 回测结束日期
- `--strategy`: 策略名称
- `--params`: 策略参数（JSON格式）
- `--data-source`: 数据源（akshare/db/mock）
- `--output`: 输出目录

### 代码中使用

```python
from quantengine.data.akshare_source import AkshareSource
from quantengine.strategies.ma_cross import MACrossStrategy
from quantengine.backtest.engine import BacktestEngine
from quantengine.backtest.report import BacktestReport

# 创建数据源
data_source = AkshareSource()

# 创建策略
strategy = MACrossStrategy(params={'fast_window': 5, 'slow_window': 20})

# 创建回测引擎
engine = BacktestEngine(data_source, strategy)

# 运行回测
portfolio = engine.run('sh000001', '2020-01-01', '2021-01-01', '1d')

# 生成报告
report = BacktestReport(portfolio, 'sh000001', 'MA Cross')
report_data = report.generate_report('./output', format='html')
```

## 添加新策略

1. 在 `strategies` 目录下创建新的策略文件，例如 `my_strategy.py`
2. 继承 `BaseStrategy` 类并实现 `run` 方法
3. 可选：实现 `get_params_schema` 静态方法定义参数验证模式

```python
from .base_strategy import BaseStrategy
import vectorbt as vbt

class MyStrategy(BaseStrategy):
    def __init__(self, params=None):
        super().__init__(params)
        self.param1 = self.params.get('param1', 10)
        self.param2 = self.params.get('param2', 20)

    @staticmethod
    def get_params_schema():
        return {
            "param1": {
                "type": "integer",
                "title": "参数1",
                "default": 10,
                "minimum": 1,
                "maximum": 100
            },
            "param2": {
                "type": "integer",
                "title": "参数2",
                "default": 20,
                "minimum": 1,
                "maximum": 100
            }
        }

    def run(self, data):
        # 实现策略逻辑
        # 返回买入和卖出信号
        entries = ...
        exits = ...
        return entries, exits
```

## 许可证

MIT
